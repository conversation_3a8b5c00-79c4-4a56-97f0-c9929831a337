package com.hydee.h3.pay.core.config;

import com.hydee.h3.pay.common.WxCommonAppletProperties;
import com.hydee.h3.pay.enums.ChannelSourceEnum;
import com.hydee.h3.pay.enums.PayChannelSourceEnum;
import com.hydee.h3.pay.enums.PayMsgEnum;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;

import java.io.Serializable;
import java.util.Objects;

/**
 * @className: WeiXinEmphasisPayChannel
 * @author: liweixie
 * @packageName: com.hydee.h3.finance.common.sync
 * @description: 支付二期微信通道扩展
 * @version: V1.1
 * date: 2021/10/27 上午11:27
 **/
public class WeiXinEmphasisPayChannel implements IMerchantConfig , Serializable {
    private static final long serialVersionUID = -8750426976858476203L;
    /**
     * 商户公钥
     */
    private String publicKey;
    /**
     * 商户私钥
     */
    private String privateKey;

    private String appId;

    /**
     * 通道商户号
     */
    private String mchId;
    /**
     * 通道服务商商户号
     */
    private String serviceMchId;
    /**
     * 服务商appid
     */
    private String serviceAppId;
    /**
     * 商户证书
     */
    private String certPath;
    /**
     * 公众号appid
     */
    private String publicAppId;
    /**
     * 小程序appid
     */
    private String appletAppId;
    /**
     * 商户证书序列号
     */
    private String customerSerialNumber;

    private String customerPrivateKey;
    /**
     * 渠道来源
     */
    private String channelSource;

    /**
     * APIv3密钥
     */
    private String secretKey;
    /**
     * 是否服务商模式 默认否 普通商户模式
     */
    private Boolean isServiceMode = false;
    /**
     * 海典静默授权使用的公众号密钥
     */
    private String appSecret;
    /**
     * 产品编号
     */
    private String productId;

    /**
     * 医保支付key
     */
    private String medicarePayKey;

    /**
     * 城市id
     */
    private String cityId;

    /**
     * 微信渠道号
     */
    private String wxChannelCode;

    public String getMedicarePayKey() {
        return medicarePayKey;
    }

    public void setMedicarePayKey(String medicarePayKey) {
        this.medicarePayKey = medicarePayKey;
    }

    public String getCityId() {
        return cityId;
    }

    public void setCityId(String cityId) {
        this.cityId = cityId;
    }

    public String getWxChannelCode() {
        return wxChannelCode;
    }

    public void setWxChannelCode(String wxChannelCode) {
        this.wxChannelCode = wxChannelCode;
    }

    public String getProductId() {
        return productId;
    }

    public void setProductId(String productId) {
        this.productId = productId;
    }

    public Boolean getServiceMode() {
        return isServiceMode;
    }

    public void setServiceMode(Boolean serviceMode) {
        isServiceMode = serviceMode;
    }

    public String getCustomerPrivateKey() {
        return customerPrivateKey;
    }

    public void setCustomerPrivateKey(String customerPrivateKey) {
        this.customerPrivateKey = customerPrivateKey;
    }

    public String getPublicKey() {
        return publicKey;
    }

    public void setPublicKey(String publicKey) {
        this.publicKey = publicKey;
    }

    public String getPrivateKey() {
        if(privateKey == null){
            return customerPrivateKey;
        }
        return privateKey;
    }

    public void setPrivateKey(String privateKey) {
        this.privateKey = privateKey;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getMchId() {
        return mchId;
    }

    public void setMchId(String mchId) {
        this.mchId = mchId;
    }

    public String getServiceMchId() {
        return serviceMchId;
    }

    public void setServiceMchId(String serviceMchId) {
        this.serviceMchId = serviceMchId;
    }

    public String getCertPath() {
        return certPath;
    }

    public void setCertPath(String certPath) {
        this.certPath = certPath;
    }

    public String getPublicAppId() {
        return publicAppId;
    }

    public void setPublicAppId(String publicAppId) {
        this.publicAppId = publicAppId;
    }

    public String getAppletAppId() {
        return appletAppId;
    }

    public void setAppletAppId(String appletAppId) {
        this.appletAppId = appletAppId;
    }

    public String getCustomerSerialNumber() {
        return customerSerialNumber;
    }

    public void setCustomerSerialNumber(String customerSerialNumber) {
        this.customerSerialNumber = customerSerialNumber;
    }

    public String getSecretKey() {
        return secretKey;
    }

    public void setSecretKey(String secretKey) {
        this.secretKey = secretKey;
    }

    public String getServiceAppId() {
        return serviceAppId;
    }

    public String getChannelSource() {
        return channelSource;
    }

    public void setChannelSource(String channelSource) {
        this.channelSource = channelSource;
    }

    public String getAppSecret() {
        return appSecret;
    }

    public void setAppSecret(String appSecret) {
        this.appSecret = appSecret;
    }

    public void setServiceAppId(String serviceAppId) {
        this.serviceAppId = serviceAppId;
    }
    public void build(WxCommonAppletProperties properties,String channelSource){
        if (StringUtils.isNotBlank(this.serviceMchId) && Objects.equals(channelSource, PayChannelSourceEnum.APPLET_MALL.getValue())) {
            BeanUtils.copyProperties(properties, this);
            isServiceMode = true;
        }
        if(StringUtils.isNotBlank(channelSource)){
            this.channelSource = channelSource;
        }
    }
    public void build(WxCommonAppletProperties properties){
        if (StringUtils.isNotBlank(this.serviceMchId) && Objects.equals(channelSource, PayChannelSourceEnum.APPLET_MALL.getValue())) {
            BeanUtils.copyProperties(properties, this);
        }
    }


    @Override
    public void validate() {

    }
    public void validate(ChannelSourceEnum sourceEnum){
        switch (sourceEnum){
            case SCAN_PAY:
                nonNull(publicAppId, "publicAppId", PayMsgEnum.QRCODE_OAUTH_CONFIG_DEFICIENCY);
                nonNull(appSecret, "appSecret",PayMsgEnum.QRCODE_OAUTH_CONFIG_DEFICIENCY);
            case APPLET:
            default:
        }
    }

}
