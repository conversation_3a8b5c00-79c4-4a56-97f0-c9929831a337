package com.hydee.h3.pay.core.yunshanfu.internal.util.converter;



import com.hydee.h3.pay.core.yunshanfu.OpenApiException;
import com.hydee.h3.pay.core.yunshanfu.OpenApiResponse;

import java.text.ParseException;

/**
 * Created by ZHANGWEI on 2016/12/2.
 */
public abstract interface Converter {
    public abstract <T extends OpenApiResponse> T toResponse(String paramString, Class<T> paramClass)
            throws OpenApiException, IllegalAccessException, InstantiationException, ParseException, ClassNotFoundException;
}
