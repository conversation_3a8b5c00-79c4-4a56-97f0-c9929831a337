package com.hydee.h3.pay.core.yunshanfu.request;

import com.hydee.h3.pay.core.yunshanfu.OpenApiRequest;
import com.hydee.h3.pay.core.yunshanfu.annotation.ApiField;
import com.hydee.h3.pay.core.yunshanfu.response.AcpInstallmentResponse;

/**
 * Created with IntelliJ IDEA.
 * User: ZHANGWEI
 * Date: 2017/12/12
 * Time: 14:51
 * To change this template use File | Settings | File Templates.
 */
public class AcpInstallmentRequest implements OpenApiRequest<AcpInstallmentResponse> {
    @ApiField(key = "merchantCode",required = true,desc = "商户号")
    private String merchantCode;
    @ApiField(key = "terminalCode",required = true,desc = "终端号")
    private String terminalCode;
    @ApiField(key = "systemTraceNum",required = true,desc = "系统跟踪号")
    private String systemTraceNum;
    @ApiField(key = "transactionAmount",required = true,desc = "交易金额")
    private String transactionAmount;
    @ApiField(key = "transactionCurrencyCode",required = true,desc = "交易币种")
    private String transactionCurrencyCode;
    @ApiField(key = "encryptedData",required = true,desc = "加密数据")
    private String encryptedData;

    public Class<AcpInstallmentResponse> responseClass() {
        return AcpInstallmentResponse.class;
    }

    public String apiVersion() {
        return "v1";
    }

    public String apiMethodName() {
        return "一次性分期";
    }

    public String serviceCode() {
        return "/unionpay/acp/installment/request";
    }

    public boolean needToken() {
        return true;
    }

    public String getMerchantCode() {
        return merchantCode;
    }

    public void setMerchantCode(String merchantCode) {
        this.merchantCode = merchantCode;
    }

    public String getTerminalCode() {
        return terminalCode;
    }

    public void setTerminalCode(String terminalCode) {
        this.terminalCode = terminalCode;
    }

    public String getSystemTraceNum() {
        return systemTraceNum;
    }

    public void setSystemTraceNum(String systemTraceNum) {
        this.systemTraceNum = systemTraceNum;
    }

    public String getTransactionAmount() {
        return transactionAmount;
    }

    public void setTransactionAmount(String transactionAmount) {
        this.transactionAmount = transactionAmount;
    }

    public String getTransactionCurrencyCode() {
        return transactionCurrencyCode;
    }

    public void setTransactionCurrencyCode(String transactionCurrencyCode) {
        this.transactionCurrencyCode = transactionCurrencyCode;
    }

    public String getEncryptedData() {
        return encryptedData;
    }

    public void setEncryptedData(String encryptedData) {
        this.encryptedData = encryptedData;
    }
}
