package com.hydee.h3.pay.core.yunshanfu.request;

import com.hydee.h3.pay.core.yunshanfu.OpenApiRequest;
import com.hydee.h3.pay.core.yunshanfu.annotation.ApiField;
import com.hydee.h3.pay.core.yunshanfu.response.IdCardVerifyResponse;

/**
 * Created with IntelliJ IDEA.
 * User: ZHANGWEI
 * Date: 2016/12/13
 * Time: 14:07
 * 所属模块：
 * 功能说明：身份证核查
 */
public class IdCardVerifyRequest implements OpenApiRequest<IdCardVerifyResponse>{

    @ApiField(key = "data",required = true,desc = "json格式字符")
    private Object data;
    public Class<IdCardVerifyResponse> responseClass() {
        return IdCardVerifyResponse.class;
    }

    public String apiVersion() {
        return "v1";
    }

    public String apiMethodName() {
        return "身份证核查(政通实名认证)";
    }

    public String serviceCode() {
        return "/datacenter/smartverification/idcard/verify";
    }

    public boolean needToken() {
        return true;
    }

    public Object getData() {
        return data;
    }

    public void setData(Object data) {
        this.data = data;
    }
}
