package com.hydee.h3.pay.core.yunshanfu.request;

import com.hydee.h3.pay.core.yunshanfu.OpenApiRequest;
import com.hydee.h3.pay.core.yunshanfu.annotation.ApiField;
import com.hydee.h3.pay.core.yunshanfu.response.Mobile3factorVerifyResponse;

/**
 * Created with IntelliJ IDEA.
 * User: ZHANGWEI
 * Date: 2016/12/13
 * Time: 14:11
 * 所属模块：
 * 功能说明：运营商三要素验证
 */
public class Mobile3factorVerifyRequest implements OpenApiRequest<Mobile3factorVerifyResponse>{

    @ApiField(key = "data",required = true,desc = "json格式字符")
    private Object data;

    public Class<Mobile3factorVerifyResponse> responseClass() {
        return Mobile3factorVerifyResponse.class;
    }

    public String apiVersion() {
        return "v1";
    }

    public String apiMethodName() {
        return "运营商三要素验证";
    }

    public String serviceCode() {
        return "/datacenter/smartverification/mobile/3factor/verify";
    }

    public boolean needToken() {
        return true;
    }

    public Object getData() {
        return data;
    }

    public void setData(Object data) {
        this.data = data;
    }
}
