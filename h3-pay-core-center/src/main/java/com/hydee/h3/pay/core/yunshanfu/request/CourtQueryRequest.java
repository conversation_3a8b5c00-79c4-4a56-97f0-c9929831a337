package com.hydee.h3.pay.core.yunshanfu.request;

import com.hydee.h3.pay.core.yunshanfu.OpenApiRequest;
import com.hydee.h3.pay.core.yunshanfu.annotation.ApiField;
import com.hydee.h3.pay.core.yunshanfu.response.CourtQueryResponse;

/**
 * Created with IntelliJ IDEA.
 * User: ZHANGWEI
 * Date: 2017/6/6
 * Time: 15:21
 * 所属模块：
 * 功能说明：法院信息查询
 */
public class CourtQueryRequest implements OpenApiRequest<CourtQueryResponse> {
    @ApiField(key = "data",required = true,desc = "json格式字符")
    private Object data;
    public Class<CourtQueryResponse> responseClass() {
        return CourtQueryResponse.class;
    }

    public String apiVersion() {
        return "v1";
    }

    public String apiMethodName() {
        return "法院信息查询";
    }

    public String serviceCode() {
        return "/datacenter/smartverification/court/query";
    }

    public boolean needToken() {
        return true;
    }
    public Object getData() {
        return data;
    }

    public void setData(Object data) {
        this.data = data;
    }
}
