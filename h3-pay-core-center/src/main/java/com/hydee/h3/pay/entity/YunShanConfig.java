package com.hydee.h3.pay.entity;

import java.util.Date;

/**
 * @Description:
 * <AUTHOR>
 * @Date 2025/5/28 10:46
 */
public class YunShanConfig {
    private Long id;

    /**
     * 应用ID
     */
    private String appId;

    /**
     * 商户编码，可以是组织机构编码也可以是商户编码
     */
    private String merchantCode;

    /**
     * 商户名称
     */
    private String merchantName;

    /**
     * 支付通道类型
     */
    private String payChannelTypeCode;

    /**
     * 支付名称，用作收银台支付方式的展示
     */
    private String payName;

    /**
     * logo，可以覆盖通道支付类型中的logo
     */
    private String logo;

    /**
     * 优先级,越小越优先，默认0
     */
    private Integer priority;

    /**
     * 是否可用 0-不可用 1-可用
     */
    private Integer enabled;

    /**
     * 配置内容
     */
    private String configContent;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
