package com.hydee.h3.pay.core.yunshanfu;

import java.io.Serializable;

/**
 * Created with IntelliJ IDEA.
 * User: ZHANGWEI
 * Date: 2016/12/2
 * Time: 10:11
 * 所属模块：
 * 功能说明：
 */
public class OpenApiException extends Exception implements Serializable{
    private String errCode;
    private String errInfo;
    public OpenApiException() {}

    public OpenApiException(String message, Throwable cause)
    {
        super(message, cause);
    }

    public OpenApiException(String message)
    {
        super(message);
    }

    public OpenApiException(Throwable cause)
    {
        super(cause);
    }

    public OpenApiException(String errCode, String errInfo)
    {
        super(errCode + ":" + errInfo);
        this.errCode = errCode;
        this.errInfo = errInfo;
    }

    public String getErrCode()
    {
        return this.errCode;
    }

    public String getErrMsg()
    {
        return this.errInfo;
    }
}
