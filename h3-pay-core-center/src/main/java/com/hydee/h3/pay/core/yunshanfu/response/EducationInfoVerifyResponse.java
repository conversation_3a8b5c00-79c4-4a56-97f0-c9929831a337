package com.hydee.h3.pay.core.yunshanfu.response;

import com.hydee.h3.pay.core.yunshanfu.OpenApiResponse;

/**
 * Created with IntelliJ IDEA.
 * User: ZHANGWEI
 * Date: 2017/5/8
 * Time: 17:03
 * 所属模块：
 * 功能说明：学历信息验证
 */
public class EducationInfoVerifyResponse extends OpenApiResponse {
    private String resultCode;
    private String resultInfo;

    public Object getData() {
        return data;
    }

    public void setData(Object data) {
        this.data = data;
    }

    private Object data;

    public String getResultCode() {
        return resultCode;
    }

    public void setResultCode(String resultCode) {
        this.resultCode = resultCode;
    }

    public String getResultInfo() {
        return resultInfo;
    }

    public void setResultInfo(String resultInfo) {
        this.resultInfo = resultInfo;
    }
}