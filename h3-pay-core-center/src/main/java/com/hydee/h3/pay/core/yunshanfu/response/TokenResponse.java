package com.hydee.h3.pay.core.yunshanfu.response;


import com.hydee.h3.pay.core.yunshanfu.OpenApiResponse;

import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 * User: ZHANGWEI
 * Date: 2016/12/5
 * Time: 15:42
 * 所属模块：
 * 功能说明：
 */
public class TokenResponse extends OpenApiResponse {
    /**
     * 服务授权令牌
     */
    private String accessToken;
    /**
     * 有效期
     */
    private int expiresIn;
    /**
     * 令牌开始生效的时间，以收到时间为准
     */
    private Date effectTime;

    /**
     * 令牌的过期时间，单位为秒，由数据中心返回
     */
    private int timeout;

    /**
     * 在令牌过期前，要提前重新申请
     */
    private int aheadInterval;

    public String getAccessToken() {
        return accessToken;
    }

    public void setAccessToken(String accessToken) {
        this.accessToken = accessToken;
    }

    public int getExpiresIn() {
        return expiresIn;
    }

    public void setExpiresIn(int expiresIn) {
        this.expiresIn = expiresIn;
    }

    public Date getEffectTime() {
        return effectTime;
    }

    public void setEffectTime(Date effectTime) {
        this.effectTime = effectTime;
    }

    public int getTimeout() {
        return timeout;
    }

    public void setTimeout(int timeout) {
        this.timeout = timeout;
    }

    public int getAheadInterval() {
        return aheadInterval;
    }

    public void setAheadInterval(int aheadInterval) {
        this.aheadInterval = aheadInterval;
    }
}
