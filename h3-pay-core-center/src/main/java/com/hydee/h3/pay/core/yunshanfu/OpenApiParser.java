package com.hydee.h3.pay.core.yunshanfu;

/**
 * Created with IntelliJ IDEA.
 * User: ZHANGWEI
 * Date: 2016/12/2
 * Time: 14:27
 * 所属模块：
 * 功能说明：
 */
public abstract interface OpenApiParser<T extends OpenApiResponse> {
    /**
     * 返回内容格式转换
     * @param paramString
     * @return
     * @throws OpenApiException
     */
    public abstract T parse(String paramString) throws OpenApiException;

    /**
     * 请求参数检查
     * @param openApiRequest
     * @return
     * @throws OpenApiException
     */
    public abstract String validRequest(OpenApiRequest<T> openApiRequest) throws OpenApiException;
}
