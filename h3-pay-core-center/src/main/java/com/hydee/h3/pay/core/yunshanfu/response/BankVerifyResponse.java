package com.hydee.h3.pay.core.yunshanfu.response;

import com.hydee.h3.pay.core.yunshanfu.OpenApiResponse;

/**
 * Created with IntelliJ IDEA.
 * User: ZHANGWEI
 * Date: 2016/12/2
 * Time: 14:14
 * 所属模块：
 * 功能说明：银行卡验证
 */
public class BankVerifyResponse extends OpenApiResponse {
    private String resultCode;
    private String resultInfo;
    private Object data;
    public Object getData() {
        return data;
    }
    public void setData(Object data) {
        this.data = data;
    }

    public String getResultCode() {
        return resultCode;
    }

    public void setResultCode(String resultCode) {
        this.resultCode = resultCode;
    }

    public String getResultInfo() {
        return resultInfo;
    }

    public void setResultInfo(String resultInfo) {
        this.resultInfo = resultInfo;
    }

}
