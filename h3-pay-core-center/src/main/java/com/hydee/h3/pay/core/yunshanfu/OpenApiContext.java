package com.hydee.h3.pay.core.yunshanfu;


import com.hydee.h3.pay.core.yunshanfu.constants.ConfigBean;
import com.hydee.h3.pay.core.yunshanfu.response.TokenResponse;

import java.util.Map;

/**
 * Created with IntelliJ IDEA.
 * User: ZHANGWEI
 * Date: 2016/12/6
 * Time: 10:17
 * 所属模块：
 * 功能说明：api 处理报文上下文
 */
public class OpenApiContext {
    private String requestId;
    private TokenResponse currentToken;
    private long startTime;
    private long endTime;
    private String request;
    private String response;
    private Map<String,Object> params;
    private String apiMethodName;
    private String serviceCode;
    private String openServUrl;
    private String version;
    private String appId;
    private String appKey;
    private ConfigBean configBean;
    private String apiServiceUrl;
    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public TokenResponse getCurrentToken() {
        return currentToken;
    }

    public void setCurrentToken(TokenResponse currentToken) {
        this.currentToken = currentToken;
    }

    public long getStartTime() {
        return startTime;
    }

    public void setStartTime(long startTime) {
        this.startTime = startTime;
    }

    public long getEndTime() {
        return endTime;
    }

    public void setEndTime(long endTime) {
        this.endTime = endTime;
    }

    public String getRequest() {
        return request;
    }

    public void setRequest(String request) {
        this.request = request;
    }

    public String getResponse() {
        return response;
    }

    public void setResponse(String response) {
        this.response = response;
    }

    public Map<String, Object> getParams() {
        return params;
    }

    public void setParams(Map<String, Object> params) {
        this.params = params;
    }

    public String getServiceCode() {
        return serviceCode;
    }

    public void setServiceCode(String serviceCode) {
        this.serviceCode = serviceCode;
    }

    public String getApiMethodName() {
        return apiMethodName;
    }

    public void setApiMethodName(String apiMethodName) {
        this.apiMethodName = apiMethodName;
    }

    public String getOpenServUrl() {
        return openServUrl;
    }

    public void setOpenServUrl(String openServUrl) {
        this.openServUrl = openServUrl;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getAppKey() {
        return appKey;
    }

    public void setAppKey(String appKey) {
        this.appKey = appKey;
    }

    public ConfigBean getConfigBean() {
        return configBean;
    }

    public void setConfigBean(ConfigBean configBean) {
        this.configBean = configBean;
    }

    public String getApiServiceUrl() {
        return apiServiceUrl;
    }

    public void setApiServiceUrl(String apiServiceUrl) {
        this.apiServiceUrl = apiServiceUrl;
    }
}
