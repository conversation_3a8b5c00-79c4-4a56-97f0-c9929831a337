package com.hydee.h3.pay.bridge.pay.impl;

import cn.hutool.core.codec.Base64;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.hydee.common.beans.exception.HCloudRuntimeException;
import com.hydee.common.beans.message.MessageData;
import com.hydee.common.util.CollectionUtils;
import com.hydee.common.util.DateUtil;
import com.hydee.common.util.JsonUtil;
import com.hydee.common.util.ObjectUtils;
import com.hydee.h3.common.distributed.SnowflakeIdWorkerNew;
import com.hydee.h3.internal.api.paycore.bean.PlatformChannelDTO;
import com.hydee.h3.internal.api.paycore.bean.request.AbstractOperationRequest;
import com.hydee.h3.internal.api.paycore.bean.request.wx.BusinessPayPurseRequest;
import com.hydee.h3.internal.api.paycore.bean.request.wx.BusinessPayeeDetail;
import com.hydee.h3.internal.api.paycore.bean.request.wx.PaySecondPhaseVO;
import com.hydee.h3.internal.api.paycore.bean.request.wx.RefundSecondPhaseVo;
import com.hydee.h3.internal.api.paycore.bean.response.ABstractOauthResponse;
import com.hydee.h3.internal.api.paycore.bean.response.AbstractEmphasisResponse;
import com.hydee.h3.internal.api.paycore.bean.response.SimplePayResponse;
import com.hydee.h3.internal.api.paycore.bean.response.wx.BusinessPayPurseDetail;
import com.hydee.h3.internal.api.paycore.bean.response.wx.BusinessPayPurseResponse;
import com.hydee.h3.internal.api.paycore.bean.response.wx.PayPrePaymentResponse;
import com.hydee.h3.internal.api.paycore.bean.response.wx.PaySecondPhaseResponse;
import com.hydee.h3.internal.api.paycore.bean.response.wx.RefundEmphasisResponse;
import com.hydee.h3.log.LoggerManager;
import com.hydee.h3.pay.bridge.pay.AbstractPayStrategyBinder;
import com.hydee.h3.pay.bridge.verify.impl.WeiXinPrepositionStrategy;
import com.hydee.h3.pay.common.HuiFuPaymentProperties;
import com.hydee.h3.pay.common.YunShanPayProperties;
import com.hydee.h3.pay.config.PayConfiguration;
import com.hydee.h3.pay.core.config.WeiXinEmphasisPayChannel;
import com.hydee.h3.pay.core.config.YunShanFuPayConfig;
import com.hydee.h3.pay.core.error.PayResponseError;
import com.hydee.h3.pay.core.rcb.RcbSignUtil;
import com.hydee.h3.pay.core.weixin.WxEmphasisPayBuilder;
import com.hydee.h3.pay.core.weixin.WxPaymentPurseBuilder;
import com.hydee.h3.pay.core.weixin.constants.WXEmphasisConstants;
import com.hydee.h3.pay.core.weixin.enums.WxPayPurseBatchStatusEnum;
import com.hydee.h3.pay.core.weixin.enums.WxPayPurseCloseReasonEnum;
import com.hydee.h3.pay.core.weixin.enums.WxPayPurseDetailStatusEnum;
import com.hydee.h3.pay.entity.BusinessPayOrder;
import com.hydee.h3.pay.entity.PayEmphasisCoreOrder;
import com.hydee.h3.pay.entity.RefundEmphasisCoreOrder;
import com.hydee.h3.pay.enums.ChannelSourceEnum;
import com.hydee.h3.pay.enums.PayChannelSourceEnum;
import com.hydee.h3.pay.enums.PayEmphasisMsgEnum;
import com.hydee.h3.pay.enums.PayEmphasisOperationEnum;
import com.hydee.h3.pay.enums.PayEmphasisStatusEnum;
import com.hydee.h3.pay.enums.PayEmphasisStrategyTableEnum;
import com.hydee.h3.pay.enums.PayModeEnum;
import com.hydee.h3.pay.enums.PayMsgEnum;
import com.hydee.h3.pay.enums.PayPurseFailReasonEnum;
import com.hydee.h3.pay.enums.PayStatusEnum;
import com.hydee.h3.pay.enums.PayTradeStateEnum;
import com.hydee.h3.pay.enums.PayTypeEnum;
import com.hydee.h3.pay.enums.PaymentStatusEnum;
import com.hydee.h3.pay.exception.PayIllegalArgumentException;
import com.hydee.h3.pay.service.IBusinessPayService;
import com.hydee.h3.pay.service.IPayEmphasisService;
import com.hydee.h3.pay.util.AssertUtil;
import com.hydee.h3.pay.util.PayUtils;
import com.hydee.h3.pay.util.WecatTokenUtils;
import com.hydee.h3.pay.util.yantai.AmountUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @className: WeiXinPayStrategyBinder
 * @author: liweixie
 * @packageName: com.hydee.h3.pay.bridge.pay.impl
 * @description: 微信支付策略
 * @version: V1.1
 * date: 2021/10/13 下午4:34
 **/
@Component
public class YunShanPayStrategyBinder extends AbstractPayStrategyBinder<WeiXinPrepositionStrategy>  {
    private static Logger log = LoggerManager.getLogger(YunShanPayStrategyBinder.class);
    @Resource(name = IPayEmphasisService.BEAN_ID)
    IPayEmphasisService payEmphasisService;
    @Autowired
    @Qualifier(PayConfiguration.PAY_ID_WORKER_BEAN_NAME)
    private SnowflakeIdWorkerNew snowflakeIdWorker;
    @Autowired
    private IBusinessPayService businessPayService;
    @Autowired
    protected YunShanPayProperties yunShanPayProperties;
    /**
     * 支付二期，微信支付行为
     *
     * @param request
     */
    @Override
    public <T extends AbstractOperationRequest> AbstractEmphasisResponse pay(T request) {
        PaySecondPhaseVO phaseVO = (PaySecondPhaseVO) request;
        phaseVO.setNotifyUrl(yunShanPayProperties.getPlantNotifyUrl());
        //封装基础响应数据给业务端
        PaySecondPhaseResponse callbackResponse = executePackagePayResponseBuilder(request);
        try {
            //获取支付配置
            YunShanFuPayConfig yunShanFuPayConfig =
            //赋予服务商模式信息

            log.info("支付---yunShanFuPayConfig:{},channelSource:{}",JSONObject.toJSONString(yunShanFuPayConfig),phaseVO.getChannelSource());
            WxEmphasisPayBuilder wxEmphasisPayBuilder = new WxEmphasisPayBuilder(merchantConfig, phaseVO);
            String url = WXEmphasisConstants.WX_JSAPI_PAY_URL;
            if (StringUtils.isNotBlank(merchantConfig.getServiceMchId())
                    && Objects.equals(phaseVO.getChannelSource(), PayChannelSourceEnum.APPLET_MALL.getValue())) {
                url = WXEmphasisConstants.WX_SERVICE_JSAPI_PAY_URL;
            }
            wxEmphasisPayBuilder.payOrder(url, (requestBody, responseBody) -> processPackageCallbackResponseBuilder(requestBody, responseBody, merchantConfig, callbackResponse,
                    phaseVO.getBusinessPayOrderCode(),phaseVO.getChannelSource()));
        } catch (PayIllegalArgumentException e) {
            log.error("微信下单，业务请求单号{},下单失败,失败原因{}", phaseVO.getBusinessPayOrderCode(),e.getMessage(),
                    e);
            PayResponseError.payEmphasisErrorBuild(callbackResponse, e);
        }catch (Exception e) {
            log.error("微信下单，业务请求单号{},下单失败,失败原因{}", phaseVO.getBusinessPayOrderCode(),e.getMessage(),
                    e);
            PayResponseError.payEmphasisErrorBuild(callbackResponse, PayMsgEnum.BIZ_ERROR);
        }
        return callbackResponse;
    }



    @Override
    public <T extends AbstractOperationRequest> AbstractEmphasisResponse payQuery(T request) {
        if(request instanceof PaySecondPhaseVO) {
            //支付查询
            PaySecondPhaseVO phaseVO = (PaySecondPhaseVO) request;
            PaySecondPhaseResponse response = null;
            if (phaseVO.getPlatformPayOrderCode() == null) {
                phaseVO.setPlatformPayOrderCode(Long.parseLong(phaseVO.getPlatformChannel().getPlatformOrderCode()));
            }
            response = (PaySecondPhaseResponse) wecatJsApi(phaseVO, request);
            return response;
        }else if(request instanceof RefundSecondPhaseVo){
            RefundSecondPhaseVo refundVo = (RefundSecondPhaseVo) request;
            //退款查询
            RefundEmphasisResponse response = null;
            response = wecatJsApiRefundQuery(refundVo,request);
            return response;
        }
        return null;
    }

    /**
     * 微信退款查询
     * @param refundVo
     * @param requst
     * @param <T>
     * @return
     */
    private <T extends AbstractOperationRequest> RefundEmphasisResponse wecatJsApiRefundQuery(RefundSecondPhaseVo refundVo, T requst) {
        RefundEmphasisResponse callbackResponse = new RefundEmphasisResponse();
        callbackResponse.setOperaType(PayEmphasisOperationEnum.REFUND_QUERY.name());
        callbackResponse.setRequestBody(JsonUtil.objectToString(requst));
        callbackResponse.setBusinessRefundOrderCode(refundVo.getBusinessRefundOrderCode());
        refundVo.setPlatformRefundOrderCode(refundVo.getPlatformChannel().getPlatformOrderCode());
        callbackResponse.setPlatformRefundOrderCode(refundVo.getPlatformRefundOrderCode());
        try {
            WeiXinEmphasisPayChannel merchantConfig = JSON.parseObject(refundVo.getPlatformChannel().getChannelConfigInfo(), WeiXinEmphasisPayChannel.class);
            //赋予服务商模式信息
            String channelSource = cacheSerivce.get(getChannelSourceRedisKey(callbackResponse.getPlatformRefundOrderCode()));
            String channelSourceJobHold = refundVo.getPlatformChannel().getChannelSource();

            if(StringUtils.isBlank(channelSource) && StringUtils.isNotBlank(channelSourceJobHold)){
                //如果redis数据不存在，则服务商模式会进行定时任务补偿更新状态
                    channelSource = channelSourceJobHold;
            }
            if((StringUtils.isBlank(channelSource) && StringUtils.isBlank(channelSourceJobHold)) && StringUtils.isNotBlank(refundVo.getPlatformRefundOrderCode())){
                //缓存没有 则从数据库中查找
                channelSource = payEmphasisService.getEmphasisPlatformChannelSource(Long.parseLong(refundVo.getPlatformRefundOrderCode()));
            }
            merchantConfig.build(wxCommonAppletProperties,channelSource);
            WxEmphasisPayBuilder<T> wxEmphasisPayBuilder = new WxEmphasisPayBuilder(merchantConfig, refundVo);
            log.info("微信退款查询之前的参数：{},merchantConfig:{},channel:{}",JSONObject.toJSONString(refundVo),JSONObject.toJSONString(merchantConfig),channelSource);
            String url = String.format(WXEmphasisConstants.WECAT_ORDER_REFUND_QUERY_URL,refundVo.getPlatformRefundOrderCode());
            wxEmphasisPayBuilder.payOrderQuery(url, new WxEmphasisPayBuilder.CallbackHandle() {
                @Override
                public void getCallBackDataHandle(String requestBody, String responseBody) {
                    callbackResponse.setRequestBody(requestBody);
                    callbackResponse.setResponseBody(responseBody);
                    //提前解析出微信返回编码，用做后续从库中查找对于平台返回的code 同codedesc
                    if (StringUtils.isNotBlank(responseBody)) {
                        Map<String, Object> resultMap = JSONObject.parseObject(responseBody, new TypeReference<Map<String, Object>>() {
                        });
                        //信息注入返回对象
                        refundQueryProcessInjectCallbackResponse(callbackResponse, resultMap);
                    } else {
                        PayResponseError.payEmphasisErrorBuild(callbackResponse, PayEmphasisMsgEnum.WX_PAY_QUERY_NOT_RETRURN_DATA);
                        if (log.isInfoEnabled()) {
                            log.info("微信退款查询，业务请求单号{},支付请求单号:{},支付询失败，请求数据{},响应数据{}", refundVo.getBusinessRefundOrderCode(),
                                    refundVo.getPlatformRefundOrderCode(), requestBody, responseBody);
                        }
                    }

                }
            });

        } catch (Exception e) {
            log.error("微信退款查询，业务请求单号{},,支付请求单号:{},查询失败,失败原因{}", refundVo.getBusinessRefundOrderCode(),
                    refundVo.getPlatformRefundOrderCode(), e.getMessage(),e);
            PayResponseError.payEmphasisErrorBuild(callbackResponse, PayMsgEnum.BIZ_ERROR);
        }
        return callbackResponse;
    }

    /**
     * 查询结果封装
     * @param callbackResponse
     * @param resultMap
     */
    private void refundQueryProcessInjectCallbackResponse(RefundEmphasisResponse callbackResponse, Map<String, Object> resultMap) {
        String trade_state = getDefaultValue(resultMap.get(WXEmphasisConstants.STATUS));
        if (Objects.equals(trade_state, PayTradeStateEnum.SUCCESS.getName())) {
            if(resultMap.get(WXEmphasisConstants.AMOUNT) != null) {
                JSONObject json = JSONObject.parseObject(resultMap.get(WXEmphasisConstants.AMOUNT).toString());
                callbackResponse.setPayerRefund(json.getInteger(WXEmphasisConstants.REFUND_PAYER));
                callbackResponse.setRefund(json.getInteger(WXEmphasisConstants.REFUND_REFUND));
                callbackResponse.setPayerTotal(json.getInteger(WXEmphasisConstants.REFUND_PAYER_TOTAL));
                callbackResponse.setTotal(json.getInteger(WXEmphasisConstants.REFUND_TOTAL));
                callbackResponse.setRefundStatus(PayStatusEnum.REFUND_SUCCESS.getValue());
                callbackResponse.setStatus(PayStatusEnum.REFUND_SUCCESS.getValue());
                callbackResponse.setPayStatus(PayStatusEnum.REFUND_SUCCESS.getValue());
            }
            callbackResponse.setResultCode(PayEmphasisMsgEnum.QUERY_AN_ORDER_SUCCESS.getCode());
            callbackResponse.setResultCodeName(PayEmphasisMsgEnum.QUERY_AN_ORDER_SUCCESS.getDisplayName());
            if(resultMap.get(WXEmphasisConstants.TIME) != null){
                callbackResponse.setDownTime(DateUtil.format(DateUtil.parse(resultMap.get(WXEmphasisConstants.TIME).toString(), WXEmphasisConstants.TIME_FORMAT),DateUtil.NORM_DATETIME_PATTERN));
            }
            callbackResponse.setUserAccount(Optional.ofNullable(resultMap.get(WXEmphasisConstants.REFUND_ACCOUNT)).orElse("默认账户").toString());
        }else{
            callbackResponse.setResultCode(getDefaultValue(resultMap.get(WXEmphasisConstants.CODE)));
            callbackResponse.setResultCodeDesc(getDefaultValue(resultMap.get(WXEmphasisConstants.MESSAGE)));
            callbackResponse.setPayStatus(PayStatusEnum.REFUND_FAIL.getValue());
        }
        //支付状态
        if(Objects.nonNull(trade_state)) {
            setRefundStatus(callbackResponse, trade_state);
        }
    }

    /**
     * 微信jsapi查询v3
     * @param phaseVO
     * @return
     */
    private  <T extends AbstractOperationRequest> AbstractEmphasisResponse wecatJsApi(PaySecondPhaseVO phaseVO, T requst) {
        PaySecondPhaseResponse callbackResponse = new PaySecondPhaseResponse();
        callbackResponse.setOperaType(PayEmphasisOperationEnum.PAY_QUERY.name());
        callbackResponse.setPayStatus(PayStatusEnum.QUERYING.getValue());
        callbackResponse.setRequestBody(JsonUtil.objectToString(requst));
        callbackResponse.setPlatformPayOrderCode(phaseVO.getPlatformPayOrderCode()+"");
        callbackResponse.setBusinessPayOrderCode(phaseVO.getBusinessPayOrderCode());
        callbackResponse.setBusinessOrderCode(phaseVO.getPlatformChannel().getBusinessOrderCode());
        try {
            WeiXinEmphasisPayChannel merchantConfig = JSON.parseObject(phaseVO.getPlatformChannel().getChannelConfigInfo(), WeiXinEmphasisPayChannel.class);
            //赋予服务商模式信息
            String channelSource = phaseVO.getPlatformChannel().getChannelSource();
            merchantConfig.build(wxCommonAppletProperties, channelSource);
            WxEmphasisPayBuilder<T> wxEmphasisPayBuilder = new WxEmphasisPayBuilder(merchantConfig, phaseVO);
            log.info("微信支付查询之前的参数：{},merchantConfig:{},channel:{}", JSONObject.toJSONString(phaseVO), JSONObject.toJSONString(merchantConfig), channelSource);
            String url = WXEmphasisConstants.WX_JSAPI_QUERY_OUT_TRADE_URL;
            if (StringUtils.isNotBlank(merchantConfig.getServiceMchId())
                    && Objects.equals(channelSource, PayChannelSourceEnum.APPLET_MALL.getValue())) {
                url = WXEmphasisConstants.WX_SERVICE_JSAPI_QUERY_OUT_TRADE_URL;
            }
            wxEmphasisPayBuilder.payOrderQuery(url, new WxEmphasisPayBuilder.CallbackHandle() {
                @Override
                public void getCallBackDataHandle(String requestBody, String responseBody) {
                    callbackResponse.setRequestBody(requestBody);
                    callbackResponse.setResponseBody(responseBody);
                    //提前解析出微信返回编码，用做后续从库中查找对于平台返回的code 同codedesc
                    if (StringUtils.isNotBlank(responseBody)) {
                        Map<String, Object> resultMap = JSONObject.parseObject(responseBody, new TypeReference<Map<String, Object>>() {
                        });
                        //信息注入返回对象
                        processInjectCallbackResponse(callbackResponse, resultMap);
                    } else {
                        PayResponseError.payEmphasisErrorBuild(callbackResponse, PayEmphasisMsgEnum.WX_PAY_QUERY_NOT_RETRURN_DATA);
                        if (log.isInfoEnabled()) {
                            log.info("微信支付查询，业务请求单号{},支付请求单号:{},支付询失败，请求数据{},响应数据{}", phaseVO.getBusinessPayOrderCode(),
                                    phaseVO.getPlatformPayOrderCode(), requestBody, responseBody);
                        }
                    }

                }
            });
        }catch (PayIllegalArgumentException e) {
                log.error("微信支付查询，业务请求单号{},,支付请求单号:{},查询失败,失败原因{}", phaseVO.getBusinessPayOrderCode(),phaseVO.getPlatformPayOrderCode(),e.getMessage(), e.getCause());
                PayResponseError.payEmphasisErrorBuild(callbackResponse, e);
        } catch (Exception e) {
            log.error("微信支付查询，业务请求单号{},,支付请求单号:{},查询失败,失败原因{}", phaseVO.getBusinessPayOrderCode(),
                    phaseVO.getPlatformPayOrderCode(), e.getMessage(),e);
            PayResponseError.payEmphasisErrorBuild(callbackResponse, PayMsgEnum.BIZ_ERROR);
        }
        return callbackResponse;
    }

    /**
     * 组装预支付数据
     *
     * @param response
     * @return
     */
    @Override
    public MessageData<AbstractEmphasisResponse> responseMessagePayDataBuild(AbstractEmphasisResponse response) {
        MessageData<AbstractEmphasisResponse> messageData = null;
        if(response instanceof PaySecondPhaseResponse){
            messageData = payData((PaySecondPhaseResponse) response);
        }else if(response instanceof RefundEmphasisResponse){
            messageData = refundData((RefundEmphasisResponse) response);
        }else if(response instanceof BusinessPayPurseResponse){
            messageData = paymentPurseData((BusinessPayPurseResponse) response);
        }
        return messageData;
    }

    /**
     * 退款数据处理
     * @param response
     * @return
     */
    private static MessageData<AbstractEmphasisResponse> refundData(RefundEmphasisResponse response) {
        try {
            if (Objects.equals(response.getStatus(), PayEmphasisStatusEnum.PAY_PLACE_AN_ORDER_SUCCESS.getValue())) {
                return MessageData.success(PayMsgEnum.OPERATION_SUCCESS, response);
            } else {
                return MessageData.build(true, false, response.getResultCode(), response.getResultCodeName(), response);
            }
        } catch (Exception e) {
            log.error("微信封装返回业务端数据异常,失败原因{}",e.getMessage(),
                    e);
            return MessageData.fail(PayEmphasisMsgEnum.OPERATION_FAIL_PACKAGE_PREPAY_ERROR, response);
        }
    }

    /**
     * 支付的数据处理
     * @param response
     * @return
     */
    public MessageData<AbstractEmphasisResponse> payData(PaySecondPhaseResponse response) {
        PaySecondPhaseResponse phaseResponse = response;
        try {
            if (Objects.equals(phaseResponse.getPayStatus(), PayEmphasisStatusEnum.PAY_PLACE_AN_ORDER_SUCCESS.getValue())) {
                JSONObject resultJson = JSONObject.parseObject(phaseResponse.getResponseBody());
                PayPrePaymentResponse payPrePaymentResponse = new PayPrePaymentResponse();
                generatePublicBackParam(phaseResponse, payPrePaymentResponse);

                String packageStr = "prepay_id=" + resultJson.get("prepay_id");
                JSONObject requstBody = JSONObject.parseObject(phaseResponse.getRequestBody(), JSONObject.class);
                //识别是服务商模式
                if(Objects.nonNull(requstBody.get("appid"))){
                    payPrePaymentResponse.setAppid(requstBody.getString("appid"));
                }else {
                    payPrePaymentResponse.setAppid(requstBody.getString("sub_appid"));
                }
                payPrePaymentResponse.setTimeStamp(String.valueOf(PayUtils.getCurrentTimestamp()));
                payPrePaymentResponse.setNonceStr(PayUtils.generateNonceStr());
                payPrePaymentResponse.setPackageStr(packageStr);
                payPrePaymentResponse.setPaySign(getSign(WXEmphasisConstants.RSA_1_256, payPrePaymentResponse, requstBody.getString("secretKey")));
                payPrePaymentResponse.setSignType("RSA");
                payPrePaymentResponse.setBusinessPayOrderCode(response.getBusinessPayOrderCode());
                payPrePaymentResponse.setPlatformPayOrderCode(response.getPlatformPayOrderCode());
                payPrePaymentResponse.setPostAmount(response.getPostAmount());
                //h5执行回调业务端
                if(Objects.nonNull(response.gethFive()) && response.gethFive()){
                    //成功则清掉redis缓存数据
                    //delRedisCacheData(phaseResponse.getBusinessPayOrderCode());
                    //执行回调业务端
                    executeCallbackBusinessRequest(phaseResponse, payPrePaymentResponse);
                }
                return MessageData.success(PayMsgEnum.OPERATION_SUCCESS, payPrePaymentResponse);
            }
            //清理返回请求体
            clearCallbackResponse(response);
            return MessageData.fail(false,PayMsgEnum.OPERATION_FAIL, response);
        } catch (Exception e) {
            log.error("微信下单，业务请求单号{},封装返回业务端预支付标识异常,失败原因{}", phaseResponse.getBusinessPayOrderCode(),e.getMessage(),
                    e);
            return MessageData.fail(false,PayEmphasisMsgEnum.OPERATION_FAIL_PACKAGE_PREPAY_ERROR, response);
        }
    }


    /**
     * 微信结果回调通知结果处理
     *
     * @param notifyMap
     * @return
     */
    @Override
    public AbstractEmphasisResponse callback(Map<String, Object> notifyMap) {
        PaySecondPhaseResponse callbackResponse = new PaySecondPhaseResponse();
        //解析通知信息 封装对象
        parseNotifyMap(notifyMap, callbackResponse);
        //通过
        return callbackResponse;
    }

    @Override
    public AbstractEmphasisResponse refundCallback(Map<String, Object> notifyMap) {
        RefundEmphasisResponse callbackResponse = new RefundEmphasisResponse();
        //解析退款通知信息 封装对象
        parseRefundNotifyMap(notifyMap, callbackResponse);
        //通过
        return callbackResponse;
    }

    /**
     * 解析退款信息封装
     * @param notifyMap
     * @param callbackResponse
     */
    private void parseRefundNotifyMap(Map<String, Object> notifyMap, RefundEmphasisResponse callbackResponse) {
        if (Objects.nonNull(notifyMap.get(WXEmphasisConstants.RESOURCE))) {
            Map<String, Object> resourceMap = JSONObject.parseObject(JsonUtil.objectToString(notifyMap.get("resource")), Map.class);
            resourceMap.put(WXEmphasisConstants.ORDER_NO,notifyMap.get(WXEmphasisConstants.ORDER_NO));
            //解密操作
            Map<String, Object> decodeResultMap = getRefundDecodeDataV3(resourceMap);
            log.info("微信退款回调解密结果：{}",JSONObject.toJSONString(decodeResultMap));
            //信息注入返回对象
            refundProcessInjectCallbackResponse(callbackResponse, decodeResultMap);
        } else {
            callbackResponse.setRefundStatus(PayStatusEnum.REFUND_FAIL.getValue());
        }
    }

    /**
     * 退款信息注入返回
     * @param callbackResponse
     * @param decodeResultMap
     */
    private void refundProcessInjectCallbackResponse(RefundEmphasisResponse callbackResponse, Map<String, Object> decodeResultMap) {
        //处理结果
        String refund_status = getDefaultValue(decodeResultMap.get(WXEmphasisConstants.REFUND_STATUS));
        if(Objects.isNull(refund_status)){
            refund_status = getDefaultValue(decodeResultMap.get(WXEmphasisConstants.STATUS));
        }
        if (Objects.equals(refund_status, PayTradeStateEnum.SUCCESS.getName())) {
            //封装 数据返回给业务端
            Map amountMap = JSONObject.parseObject(getDefaultValue(decodeResultMap.get(WXEmphasisConstants.AMOUNT)), Map.class);
            callbackResponse.setTotal(Integer.parseInt(Optional.ofNullable(amountMap.get(WXEmphasisConstants.REFUND_TOTAL)).orElse(0).toString()));
            callbackResponse.setRefund(Integer.parseInt(Optional.ofNullable(amountMap.get(WXEmphasisConstants.REFUND_REFUND)).orElse(0).toString()));
            callbackResponse.setPayerTotal(Integer.parseInt(Optional.ofNullable(amountMap.get(WXEmphasisConstants.REFUND_PAYER_TOTAL)).orElse(0).toString()));
            callbackResponse.setPayerRefund(Integer.parseInt(Optional.ofNullable(amountMap.get(WXEmphasisConstants.REFUND_PAYER)).orElse(0).toString()));
            callbackResponse.setPlatformRefundOrderCode(getDefaultValue(decodeResultMap.get(WXEmphasisConstants.PLATFOR_ORDER)));
            callbackResponse.setThirdTradeNo(getDefaultValue(decodeResultMap.get(WXEmphasisConstants.TRANSACTION_ID)));
            if(Objects.nonNull(decodeResultMap.get(WXEmphasisConstants.SUCCESS_TIME))){
                try {
                    //异常给默认回调时间
                    callbackResponse.setDownTime(DateUtil.format(DateUtil.parse(decodeResultMap.get(WXEmphasisConstants.SUCCESS_TIME).toString(), WXEmphasisConstants.TIME_FORMAT), DateUtil.NORM_DATETIME_PATTERN));
                }catch (Exception e){callbackResponse.setDownTime(DateUtil.format(new Date(),DateUtil.NORM_DATETIME_PATTERN));log.info("退款回调时间格式转化失败");}
            }
            callbackResponse.setUserAccount(getDefaultValue(decodeResultMap.get(WXEmphasisConstants.REFUND_ACCOUNT)));
            callbackResponse.setPayStatus(PayStatusEnum.REFUND_SUCCESS.getValue());
        }
        if(StringUtils.isBlank(callbackResponse.getPlatformRefundOrderCode())){
            log.error("退款回调解密数据：{},异常原因:{}",decodeResultMap,PayEmphasisMsgEnum.THIRD_PARTY_CALLBACK_INVALID.getDisplayName(),StringUtils.EMPTY);
            throw new HCloudRuntimeException(PayEmphasisMsgEnum.THIRD_PARTY_CALLBACK_INVALID);
        }
        //支付状态
        if(Objects.nonNull(refund_status)) {
            setRefundStatus(callbackResponse, refund_status);
        }
    }

    /**
     * 退款状态
     * @param callbackResponse
     * @param refund_status
     */
    private void setRefundStatus(Object callbackResponse, String refund_status) {
        switch (refund_status) {
            case WXEmphasisConstants.SUCCESS:
                setRefundCallbackResponseStatus(callbackResponse, PayStatusEnum.REFUND_SUCCESS,PayTradeStateEnum.REFUNDSUCCESS);
                break;
            case WXEmphasisConstants.CLOSE:
                setRefundCallbackResponseStatus(callbackResponse, PayStatusEnum.REFUND_FAIL,PayTradeStateEnum.REFUNDERROR);
                break;
            case WXEmphasisConstants.PROCESSING:
                setRefundCallbackResponseStatus(callbackResponse, PayStatusEnum.PAY_PLACE_AN_ORDER_SUCCESS,PayTradeStateEnum.REFUND);
                break;
            default:
                setRefundCallbackResponseStatus(callbackResponse, PayStatusEnum.REFUND_FAIL,PayTradeStateEnum.REFUNDERROR);
        }
    }

    /**
     * 设置状态
     * @param callbackResponse 响应数据
     * @param payStatusEnum 支付状态枚举
     * @param payTradeStateEnum 交易状态枚举
     */
    private void setRefundCallbackResponseStatus(Object callbackResponse, PayStatusEnum payStatusEnum,PayTradeStateEnum payTradeStateEnum) {
        RefundEmphasisResponse response = (RefundEmphasisResponse) callbackResponse;
        response.setRefundStatus(payStatusEnum.getValue());
        response.setStatus(payStatusEnum.getValue());
        response.setResultCode(payTradeStateEnum.getName());
        response.setResultCodeDesc(payTradeStateEnum.getDisplayName());
    }

    /**
     * 解密v3退款回调
     * @param resourceMap
     * @return
     */
    private Map<String, Object> getRefundDecodeDataV3(Map<String, Object> resourceMap) {
        Map<String, Object> map = new HashMap<>();
        try {
            if (resourceMap != null) {
                log.info("退款回调解密数据：{}",JSONObject.toJSONString(resourceMap));
                //查询解密密钥
                //解析
                String orderNo = resourceMap.get(WXEmphasisConstants.ORDER_NO).toString();
                PlatformChannelDTO platformChannelDTO = payEmphasisService.getEmphasisPlatformChannelByOrder(Long.parseLong(orderNo),null);
                WeiXinEmphasisPayChannel weiXinEmphasisPayChannel = JSONObject.parseObject(platformChannelDTO.getChannelConfigInfo(),WeiXinEmphasisPayChannel.class);
                //赋予服务商模式信息
                String channelSource = cacheSerivce.get(getChannelSourceRedisKey(orderNo));
                if(StringUtils.isBlank(channelSource)){
                    //缓存没有 则从数据库中查找
                    channelSource = payEmphasisService.getEmphasisPlatformChannelSource(Long.parseLong(orderNo));
                }
                weiXinEmphasisPayChannel.build(wxCommonAppletProperties,channelSource);
                log.info("退款解密----orderNo：{},merchantConfig:{},channelSource:{}",orderNo,JSONObject.toJSONString(weiXinEmphasisPayChannel),channelSource);
                String nonce = resourceMap.get("nonce").toString();
                String ciphertext = resourceMap.get("ciphertext").toString();
                String aesKeyStr = weiXinEmphasisPayChannel.getSecretKey();
                String data = WecatTokenUtils.decryptToString(resourceMap.get("associated_data").toString().getBytes(), nonce.getBytes(), ciphertext, aesKeyStr.getBytes());
                map = JSONObject.parseObject(data, Map.class);
                map.put(WXEmphasisConstants.PLATFOR_ORDER,orderNo);
            }
        }catch (Exception e){
            log.error("解析微信回调密文失败：{}",e.getMessage(),e.getCause());
        }
        return map;
    }

    /**
     * 封装返回对象
     *
     * @param response
     * @return
     */
    @Override
    public <T extends AbstractEmphasisResponse> void packageCallbackOperation(T response) {
        if(response instanceof PaySecondPhaseResponse) {
            PaySecondPhaseResponse phaseResponse = (PaySecondPhaseResponse) response;
            PayEmphasisCoreOrder corOrder = payEmphasisService.getEmphasisPayCoreOrderByIdThirdPhase(Long.valueOf(phaseResponse.getPlatformPayOrderCode()), phaseResponse.getBusinessPayOrderCode());
            //2022-02-14请求表同订单主表关联主键调整，如查询不到，则代表历史数据，需要在查询一次
            if (Objects.isNull(corOrder)) {
                corOrder = payEmphasisService.getEmphasisPayCoreOrderById(Long.valueOf(phaseResponse.getPlatformPayOrderCode()), phaseResponse.getBusinessPayOrderCode());
            }
            //封装返回业务端数据
            handleResponseData(phaseResponse, corOrder);
        }else if(response instanceof RefundEmphasisResponse) {
            RefundEmphasisResponse phaseResponse = (RefundEmphasisResponse) response;
            RefundEmphasisCoreOrder corOrder = payEmphasisService.getEmphasisRefundOrderByOrder(Long.valueOf(phaseResponse.getPlatformRefundOrderCode()));
            //封装返回业务端数据
            handleRefundResponseData(phaseResponse, corOrder);
        }else if(response instanceof BusinessPayPurseResponse){
            BusinessPayPurseResponse purseResponse = (BusinessPayPurseResponse) response;
            //判定非异常，支付中心系统异常或者第三方系统异常不包含
            if (Objects.equals(purseResponse.getResultCode(), WXEmphasisConstants.FAIL_CODE) || Objects.equals(purseResponse.getResultCode(), WXEmphasisConstants.SUCCESS_CODE)) {
                List<BusinessPayPurseDetail> paymentResultDetail = purseResponse.getPaymentResultDetail();
                List<String> platformPaymentOrderCodes = CollectionUtils.notEmpty(paymentResultDetail) ? paymentResultDetail.stream().map(BusinessPayPurseDetail::getPlatformPaymentOrderCode).collect(Collectors.toList()) : null;
                List<BusinessPayOrder> businessPayOrderByOrderCodes = businessPayService.getBusinessPayOrderByOrderCodes(platformPaymentOrderCodes, purseResponse.getPaymentBatchOrderCode());
                if (CollectionUtils.notEmpty(paymentResultDetail) && CollectionUtils.notEmpty(businessPayOrderByOrderCodes)) {
                    Map<String, BusinessPayOrder> payOrderMap = businessPayOrderByOrderCodes.stream().collect(Collectors.toMap(BusinessPayOrder::getPlatformPaymentOrderCode, Function.identity(), (k1, k2) -> k1));
                    paymentResultDetail.stream().forEach(K -> {
                        BusinessPayOrder businessPayOrder = payOrderMap.get(K.getPlatformPaymentOrderCode());
                        K.setBusinessPaymentOrderCode(ObjectUtils.isNotNull(businessPayOrder) ? businessPayOrder.getBusinessPaymentOrderCode() : null);
                        K.setAmount(ObjectUtils.isNotNull(businessPayOrder) ? (int) businessPayOrder.getPaymentAmount().getCent() : null);
                        K.setPaymentExceptionCode(ObjectUtils.isNotNull(businessPayOrder) ? businessPayOrder.getPaymentExceptionCode() : null);
                        K.setPaymentExceptionReason(ObjectUtils.isNotNull(businessPayOrder) ? businessPayOrder.getPaymentExceptionReason() : null);
                    });
                } else if (CollectionUtils.isEmpty(paymentResultDetail) && CollectionUtils.notEmpty(businessPayOrderByOrderCodes)) {
                    //数据库查询数据赋予
                    purseResponse.setPaymentResultDetail(businessPayOrderByOrderCodes.stream().map(K -> {
                        BusinessPayPurseDetail businessPayPurseDetail = new BusinessPayPurseDetail();
                        businessPayPurseDetail.setAmount(ObjectUtils.isNotNull(K.getPaymentAmount()) ? (int) K.getPaymentAmount().getCent() : null);
                        businessPayPurseDetail.setBusinessPaymentOrderCode(K.getBusinessPaymentOrderCode());
                        businessPayPurseDetail.setPlatformPaymentOrderCode(K.getPlatformPaymentOrderCode());
                        businessPayPurseDetail.setPaymentExceptionReason(K.getPaymentExceptionReason());
                        businessPayPurseDetail.setPaymentExceptionCode(K.getPaymentExceptionCode());
                        return businessPayPurseDetail;
                    }).collect(Collectors.toList()));
                }
            }

        }
    }

    /**
     * 处理退款回调结果
     * @param phaseResponse
     * @param corOrder
     */
    public void handleRefundResponseData(RefundEmphasisResponse phaseResponse, RefundEmphasisCoreOrder corOrder) {
        //封装返回业务端数据
        phaseResponse.setPlatformBusinessNo(corOrder.getPlatformBusinessNo());
        phaseResponse.setPlatformRefundOrderCode(corOrder.getRefundPlatformOrderCode().toString());
        phaseResponse.setBusinessOrderCode(corOrder.getBusinessOrderCode());
        phaseResponse.setBusinessReturnUrl(corOrder.getBusinessReturnUrl());
        phaseResponse.setBusinessRefundOrderCode(corOrder.getBusinessRefundOrderCode());
        //2022-05-16退款增加客户服务费
        phaseResponse.setCustomerServiceAmount(null != corOrder.getCustomerServiceAmount() ? AmountUtils.yuanToPoints(BigDecimal.ZERO.subtract(corOrder.getCustomerServiceAmount().getAmount())) : NumberUtils.INTEGER_ZERO);

//        phaseResponse.setRefundStatus(corOrder.getRefundStatus());
//        phaseResponse.setRefund(corOrder.getRefundOrderAmount().getAmount().intValue());

    }


    @Override
    public <T extends AbstractEmphasisResponse> String getCallbackUrl(T response) {
        if(response instanceof PaySecondPhaseResponse) {
            PaySecondPhaseResponse phaseResponse = (PaySecondPhaseResponse) response;
            return phaseResponse.getBusinessReturnUrl();
        }else if(response instanceof RefundEmphasisResponse){
            RefundEmphasisResponse phaseResponse = (RefundEmphasisResponse) response;
            return phaseResponse.getBusinessReturnUrl();
        }
        return null;
    }

    @Override
    public <T extends AbstractEmphasisResponse> AbstractEmphasisResponse getCallbackReturnCode(T response) {
        HashMap<String, Object> returnMap = null;
        if(response instanceof PaySecondPhaseResponse) {
            PaySecondPhaseResponse phaseResponse = (PaySecondPhaseResponse) response;
            if (Objects.equals(phaseResponse.getResultCode(), PayTradeStateEnum.SUCCESS.getName())) {
                returnMap = new HashMap<String, Object>(2);
                returnMap.put("code", WXEmphasisConstants.SUCCESS_CODE);
                returnMap.put("message", WXEmphasisConstants.SUCCESS_NAME);
                //缓存支付通知之后平台支付流水
                String cacheKey = getRedisKey(phaseResponse.getPlatformPayOrderCode());
                cacheSerivce.setex(cacheKey, phaseResponse.getPlatformPayOrderCode(), WXEmphasisConstants.REDIS_EXPIRE);
            } else {
                returnMap = new HashMap<String, Object>(2);
                returnMap.put("code", WXEmphasisConstants.FAIL_CODE);
                returnMap.put("message", WXEmphasisConstants.FAIL_NAME);
            }
        }else if(response instanceof RefundEmphasisResponse){
            RefundEmphasisResponse phaseResponse = (RefundEmphasisResponse) response;
            if (Objects.equals(phaseResponse.getResultCode(), PayTradeStateEnum.SUCCESS.getName())) {
                returnMap = new HashMap<String, Object>(2);
                returnMap.put("code", WXEmphasisConstants.SUCCESS_CODE);
                returnMap.put("message", WXEmphasisConstants.SUCCESS_NAME);
                //缓存支付退款通知之后平台退款流水
                String cacheKey = getRedisKey(phaseResponse.getPlatformRefundOrderCode());
                cacheSerivce.setex(cacheKey, phaseResponse.getPlatformRefundOrderCode(), WXEmphasisConstants.REDIS_EXPIRE);
            } else {
                returnMap = new HashMap<String, Object>(2);
                returnMap.put("code", WXEmphasisConstants.FAIL_CODE);
                returnMap.put("message", WXEmphasisConstants.FAIL_NAME);
            }
        }
        SimplePayResponse simplePayResponse = JSONObject.parseObject(JsonUtil.objectToString(returnMap), SimplePayResponse.class);
        return simplePayResponse;
    }
    /**
     * 解析通知信息 封装返回对象
     *
     * @param notifyMap
     * @param callbackResponse
     */
    public void parseNotifyMap(Map<String, Object> notifyMap, PaySecondPhaseResponse callbackResponse) {
        if (Objects.nonNull(notifyMap.get(WXEmphasisConstants.RESOURCE))) {
            Map<String, Object> resourceMap = JSONObject.parseObject(JsonUtil.objectToString(notifyMap.get("resource")), Map.class);
            //是否合单支付
            resourceMap.put("attach",notifyMap.get("attach"));
            //解密操作
            Map<String, Object> decodeResultMap = getDecodeData(resourceMap);
            log.info("微信回调解密结果:{}",JSONObject.toJSONString(decodeResultMap));
            callbackResponse.setPlatformPayOrderCode(decodeResultMap.get(WXEmphasisConstants.PLATFOR_ORDER).toString());
            //信息注入返回对象
            processInjectCallbackResponse(callbackResponse, decodeResultMap);
        } else {
            callbackResponse.setPayStatus(PayStatusEnum.FAIL.getValue());
        }
    }

    /**
     * 信息注入返回对象
     *
     * @param callbackResponse 返回对象
     * @param decodeResultMap  第三方返回数据数据
     */
    public void processInjectCallbackResponse(PaySecondPhaseResponse callbackResponse, Map<String, Object> decodeResultMap) {
        //封装
        normalOrderProcess(callbackResponse, decodeResultMap);
    }

    /**
     * 正常单 非合单处理
     *
     * @param callbackResponse
     * @param decodeResultMap
     */
    public void normalOrderProcess(PaySecondPhaseResponse callbackResponse, Map<String, Object> decodeResultMap) {
        //处理结果
        String trade_state = getDefaultValue(decodeResultMap.get(WXEmphasisConstants.TRADE_STATE));
        if (Objects.equals(trade_state, PayTradeStateEnum.SUCCESS.getName())) {
            //封装 数据返回给业务端
            Map amountMap = JSONObject.parseObject(getDefaultValue(decodeResultMap.get(WXEmphasisConstants.AMOUNT)), Map.class);
            callbackResponse.setPayAmount(Objects.nonNull(amountMap.get(WXEmphasisConstants.TOTAL)) ? Integer.parseInt(amountMap.get(WXEmphasisConstants.TOTAL).toString()) : 0);
            Date successTime = DateUtil.parse(getDefaultValue(decodeResultMap.get(WXEmphasisConstants.SUCCESS_TIME)),WXEmphasisConstants.TIME_FORMAT);
            callbackResponse.setDownTime(DateUtil.formatDateTime(successTime));
            Map payerMap = JSONObject.parseObject(getDefaultValue(decodeResultMap.get(WXEmphasisConstants.PAYER)), Map.class);
             if(Objects.nonNull(decodeResultMap.get(WXEmphasisConstants.MCHID))){
                 callbackResponse.setThirdUserAccount(getDefaultValue(payerMap.get(WXEmphasisConstants.OPENID)));
             }else {
                 callbackResponse.setThirdUserAccount(getDefaultValue(payerMap.get(WXEmphasisConstants.SUB_OPENID)));
             }
            callbackResponse.setThirdTradeNo(getDefaultValue(decodeResultMap.get(WXEmphasisConstants.TRANSACTION_ID)));
        }else{
            callbackResponse.setResultCode(getDefaultValue(decodeResultMap.get(WXEmphasisConstants.CODE)));
            callbackResponse.setResultCodeDesc(getDefaultValue(decodeResultMap.get(WXEmphasisConstants.MESSAGE)));
        }

        //支付状态
        if(Objects.nonNull(trade_state)) {
            setPayStatus(callbackResponse, trade_state);
        }
    }

    public String getDefaultValue(Object obj) {
        return Objects.nonNull(obj) ? obj.toString() : null;
    }

    /**
     * 设置支付状态
     *
     * @param callbackResponse
     * @param trade_state
     */
    public void setPayStatus(Object callbackResponse, String trade_state) {
        switch (trade_state) {
            case WXEmphasisConstants.SUCCESS:
                setCallbackResponseStatus(callbackResponse, PayStatusEnum.PAY_SUCCESS, PayTradeStateEnum.SUCCESS);
                break;
            case WXEmphasisConstants.USERPAYING:
                setCallbackResponseStatus(callbackResponse, PayStatusEnum.PAYING, PayTradeStateEnum.USERPAYING);
                break;
            case WXEmphasisConstants.CLOSED:
                setCallbackResponseStatus(callbackResponse, PayStatusEnum.CLOSED, PayTradeStateEnum.CLOSED);
                break;
            case WXEmphasisConstants.NOTPAY:
                setCallbackResponseStatus(callbackResponse, PayStatusEnum.NOT_PAY, PayTradeStateEnum.NOTPAY);
                break;
            case WXEmphasisConstants.PAYERROR:
                setCallbackResponseStatus(callbackResponse, PayStatusEnum.PAY_FAIL, PayTradeStateEnum.PAYERROR);
                break;
            case WXEmphasisConstants.REFUND:
                setCallbackResponseStatus(callbackResponse, PayStatusEnum.REFUNDING, PayTradeStateEnum.REFUND);
                break;
            case WXEmphasisConstants.REVOKED:
                setCallbackResponseStatus(callbackResponse, PayStatusEnum.REVOKED, PayTradeStateEnum.REVOKED);
                break;
            default:
                setCallbackResponseStatus(callbackResponse, PayStatusEnum.FAIL, PayTradeStateEnum.PAYERROR);

        }
    }


    private void setCallbackResponseStatus(Object callbackResponse, PayStatusEnum payStatusEnum, PayTradeStateEnum payTradeStateEnum) {
        if (PaySecondPhaseResponse.class.isAssignableFrom(callbackResponse.getClass())) {
            PaySecondPhaseResponse phaseResponse = (PaySecondPhaseResponse) callbackResponse;
            phaseResponse.setPayStatus(payStatusEnum.getValue());
            phaseResponse.setResultCode(payTradeStateEnum.getName());
            phaseResponse.setResultCodeDesc(payTradeStateEnum.getDisplayName());
        }
    }

    @Override
    public <T extends AbstractOperationRequest> AbstractEmphasisResponse refund(T request) {
        RefundSecondPhaseVo refundSecondPhaseVo = (RefundSecondPhaseVo) request;
        refundSecondPhaseVo.setPlatformPayOrderCode(refundSecondPhaseVo.getPlatformChannel().getPlatformOrderCode());
        refundSecondPhaseVo.setNotifyUrl(wxPaymentProperties.getPlantRefundNotifyUrl());
        RefundEmphasisResponse callbackResponse = new RefundEmphasisResponse();
        callbackResponse.setRequestBody(JsonUtil.objectToString(request));
        callbackResponse.setPlatformRefundOrderCode(String.valueOf(refundSecondPhaseVo.getPlatformOrderCode()));
        callbackResponse.setPlatformOrderCode(refundSecondPhaseVo.getPlatformPayOrderCode());
        callbackResponse.setBusinessOrderCode(refundSecondPhaseVo.getBusinessOrderCode());
        callbackResponse.setBusinessReturnUrl(refundSecondPhaseVo.getBusinessReturnUrl());
        callbackResponse.setBusinessRefundOrderCode(refundSecondPhaseVo.getBusinessRefundOrderCode());
        try {
            WeiXinEmphasisPayChannel merchantConfig = JSON.parseObject(refundSecondPhaseVo.getPlatformChannel().getChannelConfigInfo(), WeiXinEmphasisPayChannel.class);
            //赋予服务商模式信息
            String channelSource = Objects.nonNull(refundSecondPhaseVo.getPlatformChannel()) ? refundSecondPhaseVo.getPlatformChannel().getChannelSource() : StringUtils.EMPTY;
            cacheSerivce.setex(getChannelSourceRedisKey(callbackResponse.getPlatformRefundOrderCode()),channelSource, WXEmphasisConstants.REDIS_EXPIRE_PERSISTENT);
            merchantConfig.build(wxCommonAppletProperties, channelSource);
            log.info("退款---退款redis存储key:{}，merchantConfig:{},channel:{}",callbackResponse.getPlatformRefundOrderCode(),JSONObject.toJSONString(merchantConfig),channelSource);
            WxEmphasisPayBuilder wxEmphasisPayBuilder = new WxEmphasisPayBuilder(merchantConfig, refundSecondPhaseVo);
            wxEmphasisPayBuilder.refundOrder(WXEmphasisConstants.WECAT_ORDER_REFUND_URL, new WxEmphasisPayBuilder.CallbackHandle() {
                @Override
                public void getCallBackDataHandle(String requestBody, String responseBody) {
                    //退款返回结果封装
                    processPackageRefundCallbackResponseBuilder(requestBody, responseBody, callbackResponse, refundSecondPhaseVo);
                }
            });
        } catch (Exception e) {
            log.error("微信退款下单，业务请求单号{},下单失败,失败原因{}", refundSecondPhaseVo.getBusinessRefundOrderCode(),
                    e.getMessage(),e.getCause());
            PayResponseError.payEmphasisErrorBuild(callbackResponse, PayMsgEnum.BIZ_ERROR);
        }
        return callbackResponse;
    }

    /**
     * 退款返回结果封装
     * @param requestBody 请求参数
     * @param responseBody  返回参数
     * @param callbackResponse  回调参数
     * @param refundSecondPhaseVo
     */
    public void processPackageRefundCallbackResponseBuilder(String requestBody, String responseBody, RefundEmphasisResponse callbackResponse, RefundSecondPhaseVo refundSecondPhaseVo) {
        JSONObject requst = JSONObject.parseObject(requestBody);
        //完了之后给处理返回数据加密使用
        requestBody = JSONObject.toJSONString(requst);
        callbackResponse.setRequestBody(requestBody);
        callbackResponse.setResponseBody(responseBody);
        //提前解析出微信返回编码，用做后续从库中查找对于平台返回的code 同codedesc
        if (StringUtils.isNotBlank(responseBody)) {
            JSONObject respJson = JSONObject.parseObject(responseBody);
            log.info("退款返回:{}",respJson.toJSONString());
            if (Objects.nonNull(respJson.get("status")) &&
                    (Objects.equals(respJson.get("status").toString(), WXEmphasisConstants.SUCCESS_CODE) || Objects.equals(respJson.get("status").toString(),WXEmphasisConstants.PROCESSING))) {
                callbackResponse.setStatus(PayEmphasisStatusEnum.PAY_PLACE_AN_ORDER_SUCCESS.getValue());
                callbackResponse.setRefundStatus(PayEmphasisStatusEnum.PAY_PLACE_AN_ORDER_SUCCESS.getValue());
                callbackResponse.setResultCode(PayEmphasisMsgEnum.REFUND_AN_ORDER_SUCCESS.getName());
                callbackResponse.setResultCodeName(PayEmphasisMsgEnum.REFUND_AN_ORDER_SUCCESS.getDisplayName());
                callbackResponse.setPlatformRefundOrderCode(respJson.get("out_refund_no").toString());
                callbackResponse.setPayStatus(PayStatusEnum.REFUNDING.getValue());
                callbackResponse.setResultCodeDesc(PayEmphasisMsgEnum.REFUND_AN_ORDER_SUCCESS.getDisplayName());
            } else {
                callbackResponse.setStatus(PayEmphasisStatusEnum.PAY_PLACE_AN_ORDER_FAIL.getValue());
                callbackResponse.setRefundStatus(PayStatusEnum.REFUND_FAIL.getValue());
                callbackResponse.setResultCode(respJson.get("code").toString());
                callbackResponse.setResultCodeName(respJson.get("message").toString());
                callbackResponse.setPayStatus(PayStatusEnum.REFUND_FAIL.getValue());
                callbackResponse.setResultCodeDesc(respJson.get("message").toString());
                if (log.isInfoEnabled()) {
                    log.info("微信退款下单，业务请求单号{},下单失败，请求数据{},响应数据{}", refundSecondPhaseVo.getBusinessRefundOrderCode(),
                            requestBody, responseBody);
                }
            }
        } else {
            PayResponseError.payEmphasisErrorBuild(callbackResponse, PayEmphasisMsgEnum.WX_NOT_RETRURN_DATA);
            if (log.isInfoEnabled()) {
                log.info("微信退款下单，业务请求单号{},支付询失败，请求数据{},响应数据{}", refundSecondPhaseVo.getBusinessRefundOrderCode(),
                        requestBody, responseBody);
            }
        }
    }

    @Override
    public String getPayCodeType() {
        return PayEmphasisStrategyTableEnum.WEIXINPAY.getValue();
    }

    /**
     * 解密返回结果
     *
     * @param resourceMap
     * @return
     */
    public Map<String, Object> getDecodeData(Map<String, Object> resourceMap){
        Map<String, Object> map = new HashMap<>();
        try {
            if (resourceMap != null) {
                //查询解密密钥
                PaySecondPhaseVO payVO = new PaySecondPhaseVO();
                payVO.setRequestType(PayEmphasisOperationEnum.PAY_QUERY.getValue());
                payVO.setPayTypeCode(PayModeEnum.WX_JSAPI.getValue());
                //解析
                String attach = resourceMap.get("attach").toString();
                String[] attchs = attach.split("-");
                payVO.setPlatformPayOrderCode(Long.valueOf(attchs[0]));
                PlatformChannelDTO platformChannelDTO = payEmphasisService.getEmphasisPlatformChannel(payVO);
                WeiXinEmphasisPayChannel weiXinEmphasisPayChannel = JSONObject.parseObject(platformChannelDTO.getChannelConfigInfo(),WeiXinEmphasisPayChannel.class);
                //赋予服务商模式信息
                weiXinEmphasisPayChannel.build(wxCommonAppletProperties,platformChannelDTO.getChannelSource());
                log.info("支付解密----merchantConfig:{},channel:{}",JSONObject.toJSONString(weiXinEmphasisPayChannel),platformChannelDTO.getChannelSource());
                String nonce = resourceMap.get("nonce").toString();
                String ciphertext = resourceMap.get("ciphertext").toString();
                String aesKeyStr = weiXinEmphasisPayChannel.getSecretKey();
                String data = WecatTokenUtils.decryptToString(resourceMap.get("associated_data").toString().getBytes(), nonce.getBytes(), ciphertext, aesKeyStr.getBytes());
                map = JSONObject.parseObject(data, Map.class);
                map.put(WXEmphasisConstants.PLATFOR_ORDER,payVO.getPlatformPayOrderCode());
            }
        }catch (Exception e){
            log.error("解析微信回调密文失败：{}",e.getMessage(),e);
        }
        return map;
    }


    /**
     * 获取签名
     *
     * @param signType      签名类型
     * @param response      参数对象
     * @param mchPrivateKey 商户私钥
     * @return 签名
     * @throws Exception
     */
    public static String getSign(String signType, AbstractEmphasisResponse response, String mchPrivateKey) throws Exception {
        String preStr = buildParamString(response);
        return RcbSignUtil.sign(preStr, signType, mchPrivateKey);
    }

    /**
     * 构造签名串
     *
     * @param response
     * @return
     */
    public static String buildParamString(AbstractEmphasisResponse response) throws Exception {
        StringBuilder sb = new StringBuilder();
        if (Objects.nonNull(response)) {
            Map<String, Object> packageMap = JSONObject.parseObject(JSONObject.toJSONString(response), Map.class);
            sb.append(packageMap.get("appid")).append("\n");
            sb.append(packageMap.get("timeStamp")).append("\n");
            sb.append(packageMap.get("nonceStr")).append("\n");
            sb.append(packageMap.get("packageStr")).append("\n");
        }
        return sb.toString();
    }

    public static HashMap<String, Object> getWxCallbackResponse() {
        HashMap<String, Object> callMap = new HashMap<String, Object>(2);
        callMap.put("code", "FAIL");
        callMap.put("message", "失败");
        return callMap;
    }

    @Override
    public <T extends AbstractOperationRequest> AbstractEmphasisResponse paymentPurseBehavior(T request) {
        //构建参数
        BusinessPayPurseRequest businessPayRequest = (BusinessPayPurseRequest) request;
        BusinessPayPurseResponse businessPayResponse = new BusinessPayPurseResponse();
        PlatformChannelDTO platformChannel = request.getPlatformChannel();
        try {
            WeiXinEmphasisPayChannel merchantConfig = JSON.parseObject(platformChannel.getChannelConfigInfo(), WeiXinEmphasisPayChannel.class);
            log.info("转账到零钱---merchantConfig:{},channelSource:{}", JSONObject.toJSONString(merchantConfig), businessPayRequest.getChannelSource());
            WxPaymentPurseBuilder<BusinessPayPurseRequest> wxEmphasisPayBuilder = new WxPaymentPurseBuilder(merchantConfig, businessPayRequest);
            //新数据赋予
            businessPayRequest.setPaymentBatchOrderCode(StringUtils.isNotBlank(businessPayRequest.getPaymentBatchOrderCode()) ? businessPayRequest.getPaymentBatchOrderCode() : String.valueOf(snowflakeIdWorker.nextId()));
            List<BusinessPayeeDetail> businessPayeeDetail = businessPayRequest.getBusinessPayeeDetail();
            //总金额
            Integer totalAmount = businessPayeeDetail.stream().map(item -> {
                AssertUtil.notNull(item.getAmount(),PayMsgEnum.PARAM_VALIDATE_ERROR);
                //流水号赋予
                item.setPlatformPaymentOrderCode(String.valueOf(snowflakeIdWorker.nextId()));

                return item;
            }).collect(Collectors.reducing(0, x -> x.getAmount(), (a, b) -> a + b));
            businessPayRequest.setTotalAmount(totalAmount);
            businessPayRequest.setTotalNum(businessPayeeDetail.size());

            //响应数据封装
            businessPayResponse.setOperaType(PayEmphasisOperationEnum.BUSINESS_PAY_PURSE.getName());
            businessPayResponse.setRequestBody(JsonUtil.objectToString(businessPayRequest));
            businessPayResponse.setChannelCode(PayTypeEnum.WX.getValue());
            businessPayResponse.setPaymentOrderCode(businessPayRequest.getPaymentOrderCode());
            businessPayResponse.setPaymentBatchOrderCode(businessPayRequest.getPaymentBatchOrderCode());
            businessPayResponse.setPaymentResultDetail(JSONObject.parseArray(JsonUtil.objectToString(businessPayRequest.getBusinessPayeeDetail()), BusinessPayPurseDetail.class));


            wxEmphasisPayBuilder.businessPayPurseRequest(WXEmphasisConstants.BUSINESS_PAYMENT_PURSE_URL, (requestBody, responseBody) -> {
                log.info("微信付款,业务付款单号:{},平台批次单号：{},微信响应数据{}", businessPayRequest.getPaymentOrderCode(), businessPayRequest.getPaymentBatchOrderCode(), responseBody);
                businessPayResponse.setRequestBody(requestBody);
                businessPayResponse.setResponseBody(responseBody);

                //提前解析出微信返回编码，用做后续从库中查找对于平台返回的code 同codedesc
                if (StringUtils.isNotBlank(responseBody)) {
                    JSONObject respJson = JSONObject.parseObject(responseBody);
                    String batchId = respJson.getString("batch_id");
                    if (StringUtils.isNotBlank(batchId)) {
                        businessPayResponse.setChannelBatchCode(batchId);
                        businessPayResponse.setResultCode(PayTradeStateEnum.SUCCESS.getName());
                        businessPayResponse.setResultCodeDesc(PaymentStatusEnum.PAYMENT_ING.getName());
                        businessPayResponse.setStatus(PaymentStatusEnum.PAYMENT_ING.getValue());
                        businessPayResponse.setPayStatus(PaymentStatusEnum.PAYMENT_ING.getValue());
                        Date batchCreateTime = DateUtil.parse(respJson.getString(WXEmphasisConstants.TIME), WXEmphasisConstants.TIME_FORMAT);
                        businessPayResponse.setDownTime(ObjectUtils.isNotNull(batchCreateTime) ? DateUtil.formatDateTime(batchCreateTime) : null);

                    } else {
                        //异常处理
                        processPaymentPurseExceptionHandle(businessPayResponse, respJson);
                        log.info("微信转账到零钱，业务请求单号{},转账失败，请求数据{},响应数据{}", businessPayRequest.getPaymentOrderCode(), requestBody, responseBody);
                    }

                } else {
                    PayResponseError.payEmphasisErrorBuild(businessPayResponse, PayEmphasisMsgEnum.WX_NOT_RETRURN_DATA);
                    log.info("微信转账到零钱，业务请求单号{},转账失败，请求数据{},响应数据{}", businessPayRequest.getPaymentOrderCode(), requestBody, responseBody);
                }

            });
        } catch (HCloudRuntimeException e) {
            log.error("微信转账到零钱，业务请求单号{},转账失败,失败原因{}", businessPayRequest.getPaymentOrderCode(), e.getDisplayName(), e.getCause());
            PayResponseError.payEmphasisErrorBuild(businessPayResponse, e);
            businessPayResponse.setPayStatus(PaymentStatusEnum.PAYMENT_FAIL.getValue());
        } catch (Exception e) {
            log.error("微信转账到零钱，业务请求单号{},转账失败,失败原因{}", businessPayRequest.getPaymentOrderCode(), e.getMessage(), e.getCause());
            PayResponseError.payEmphasisErrorBuild(businessPayResponse, PayMsgEnum.PAYMENT_PURSE_FAIL);
            businessPayResponse.setPayStatus(PaymentStatusEnum.PAYMENT_FAIL.getValue());
        }
        return businessPayResponse;
    }

    @Override
    public <T extends AbstractOperationRequest> AbstractEmphasisResponse paymentPurseQuery(T request) {
        //构建参数
        BusinessPayPurseRequest businessPayRequest = (BusinessPayPurseRequest) request;
        BusinessPayPurseResponse businessPayResponse = new BusinessPayPurseResponse();
        PlatformChannelDTO platformChannel = request.getPlatformChannel();

        try {
            WeiXinEmphasisPayChannel merchantConfig = JSON.parseObject(platformChannel.getChannelConfigInfo(), WeiXinEmphasisPayChannel.class);
            log.info("转账到零钱查询---merchantConfig:{}", JSONObject.toJSONString(merchantConfig));
            WxPaymentPurseBuilder<BusinessPayPurseRequest> wxEmphasisPayBuilder = new WxPaymentPurseBuilder(merchantConfig, businessPayRequest);
            //请求数据赋予
            businessPayRequest.setPaymentOrderCode(platformChannel.getBusinessOrderCode());
            businessPayRequest.setPaymentBatchOrderCode(platformChannel.getPlatformOrderCode());
            //响应数据封装
            businessPayResponse.setOperaType(PayEmphasisOperationEnum.BUSINESS_PAY_PURSE_QUERY.getName());
            businessPayResponse.setRequestBody(JsonUtil.objectToString(businessPayRequest));
            businessPayResponse.setChannelCode(PayTypeEnum.WX.getValue());
            businessPayResponse.setPaymentOrderCode(platformChannel.getBusinessOrderCode());
            businessPayResponse.setPaymentBatchOrderCode(platformChannel.getPlatformOrderCode());

            wxEmphasisPayBuilder.businessPayPurseQueryRequest(wxEmphasisPayBuilder.getPurseQueryUrl(), (requestBody, responseBody) -> {
                log.info("微信付款查询,业务付款单号:{},平台批次单号：{},微信响应数据{}", businessPayRequest.getPaymentOrderCode(), businessPayRequest.getPaymentBatchOrderCode(), responseBody);
                businessPayResponse.setRequestBody(requestBody);
                businessPayResponse.setResponseBody(responseBody);

                //提前解析出微信返回编码，用做后续从库中查找对于平台返回的code 同codedesc
                if (StringUtils.isNotBlank(responseBody)) {
                    JSONObject respJson = JSONObject.parseObject(responseBody);
                    //当走明细查单则返回参数获取不同，解析不同
                    if(StringUtils.isNotBlank(businessPayRequest.getPlatformPaymentOrderCode())){
                        if(StringUtils.isNotBlank(respJson.getString("out_batch_no"))){
                            BusinessPayPurseDetail businessPayPurseDetail = new BusinessPayPurseDetail();
                            businessPayPurseDetail.setPlatformPaymentOrderCode(respJson.getString("out_detail_no"));
                            businessPayPurseDetail.setPaymentStatus(WxPayPurseDetailStatusEnum.getPurseDetailStatusByName(respJson.getString("detail_status")));
                            businessPayPurseDetail.setChannelPayNo(respJson.getString("detail_id"));
                            String failReason = respJson.getString("fail_reason");
                            businessPayPurseDetail.setPaymentExceptionCode(failReason);
                            businessPayPurseDetail.setPaymentExceptionReason(PayPurseFailReasonEnum.getEnumByCode(failReason));
                            businessPayResponse.setPaymentResultDetail(Lists.newArrayList(businessPayPurseDetail));
                            //处理
                            processPaymentPurseResultHandle(businessPayResponse, businessPayPurseDetail);
                        }else{
                            //2022-07-11调整 start
                            exceptionFillPayPurseDetailData(businessPayRequest, businessPayResponse, respJson);
                            //异常处理
                            processPaymentPurseExceptionHandle(businessPayResponse, respJson);
                            log.info("微信转账到零钱查询，业务请求单号{},转账失败，请求数据{},响应数据{}", businessPayRequest.getPaymentOrderCode(), requestBody, responseBody);
                        }
                    }else{
                        String transferDetail = respJson.getString("transfer_detail_list");
                        List<Map<String, String>> transferDetailList = StringUtils.isNotBlank(transferDetail) ? JsonUtil.stringToTypeReference(transferDetail, new TypeReference<List<Map<String, String>>>() {
                        }) : null;
                        String transferBatch = respJson.getString("transfer_batch");
                        if (CollectionUtils.notEmpty(transferDetailList)) {
                            List<BusinessPayPurseDetail> payPurseDetails = transferDetailList.stream().map(item -> {
                                BusinessPayPurseDetail businessPayPurseDetail = new BusinessPayPurseDetail();
                                businessPayPurseDetail.setPlatformPaymentOrderCode(item.get("out_detail_no"));
                                businessPayPurseDetail.setPaymentStatus(WxPayPurseDetailStatusEnum.getPurseDetailStatusByName(item.get("detail_status")));
                                businessPayPurseDetail.setChannelPayNo(item.get("detail_id"));
                                return businessPayPurseDetail;
                            }).collect(Collectors.toList());
                            businessPayResponse.setPaymentResultDetail(payPurseDetails);
                            //随机获取明细一条数据作主状态显示
                            BusinessPayPurseDetail businessPayPurseDetail = payPurseDetails.stream().findFirst().orElseGet(BusinessPayPurseDetail::new);
                            //处理
                            processPaymentPurseResultHandle(businessPayResponse, businessPayPurseDetail);

                        }else if(StringUtils.isNotBlank(transferBatch) && CollectionUtils.isEmpty(transferDetailList)){
                            JSONObject transferBatchJson = JSONObject.parseObject(transferBatch);
                            //2022-07-11调整 start
                            List<BusinessPayeeDetail> businessPayeeDetail = businessPayRequest.getBusinessPayeeDetail();
                            if(CollectionUtils.notEmpty(businessPayeeDetail)){
                                List<BusinessPayPurseDetail> payPurseDetails = businessPayeeDetail.stream().map(item -> {
                                    BusinessPayPurseDetail businessPayPurseDetail = new BusinessPayPurseDetail();
                                    businessPayPurseDetail.setPlatformPaymentOrderCode(item.getPlatformPaymentOrderCode());
                                    businessPayPurseDetail.setBusinessPaymentOrderCode(item.getBusinessPaymentOrderCode());
                                    WxPayPurseBatchStatusEnum batch_status = WxPayPurseBatchStatusEnum.getPurseBatchStatusByCode(transferBatchJson.getString("batch_status"));
                                    switch (batch_status){
                                        case WAIT_PAY:
                                        case ACCEPTED:
                                        case FINISHED:
                                        case PROCESSING:
                                            businessPayPurseDetail.setPaymentStatus(WxPayPurseDetailStatusEnum.PROCESSING.getValue());
                                            businessPayPurseDetail.setPaymentExceptionCode(batch_status.getValue());
                                            businessPayPurseDetail.setPaymentExceptionReason(batch_status.getName());
                                            break;
                                        case CLOSED:
                                            businessPayPurseDetail.setPaymentStatus(WxPayPurseDetailStatusEnum.FAIL.getValue());
                                            businessPayPurseDetail.setPaymentExceptionCode(batch_status.getValue());
                                            businessPayPurseDetail.setPaymentExceptionReason(WxPayPurseCloseReasonEnum.getCloseReasonName(batch_status.getValue()));
                                            break;
                                        default:
                                    }

                                    return businessPayPurseDetail;
                                }).collect(Collectors.toList());
                                businessPayResponse.setPaymentResultDetail(payPurseDetails);
                                //随机获取明细一条数据作主状态显示
                                BusinessPayPurseDetail businessPayPurseDetail = payPurseDetails.stream().findFirst().orElseGet(BusinessPayPurseDetail::new);
                                //处理
                                processPaymentPurseBatchStatusResultHandle(businessPayResponse, businessPayPurseDetail);
                                //2022-07-11调整 end
                            }else{
                                //为空默认为转账中
                                businessPayResponse.setResultCode(PayTradeStateEnum.SUCCESS.getName());
                                businessPayResponse.setResultCodeDesc(PaymentStatusEnum.PAYMENT_ING.getName());
                                businessPayResponse.setStatus(PaymentStatusEnum.PAYMENT_ING.getValue());
                                businessPayResponse.setPayStatus(PaymentStatusEnum.PAYMENT_ING.getValue());
                            }


                        } else {
                            //2022-07-11调整 start
                            exceptionFillPayPurseDetailData(businessPayRequest, businessPayResponse, respJson);
                            //2022-07-11调整 end
                            //异常处理
                            processPaymentPurseExceptionHandle(businessPayResponse, respJson);
                            log.info("微信转账到零钱查询，业务请求单号{},转账失败，请求数据{},响应数据{}", businessPayRequest.getPaymentOrderCode(), requestBody, responseBody);
                        }

                    }

                } else {
                    PayResponseError.payEmphasisErrorBuild(businessPayResponse, PayEmphasisMsgEnum.WX_NOT_RETRURN_DATA);
                    log.info("微信转账到零钱查询，业务请求单号{},转账失败，请求数据{},响应数据{}", businessPayRequest.getPaymentOrderCode(), requestBody, responseBody);
                }

            });
        } catch (HCloudRuntimeException e) {
            log.error("微信转账到零钱查询，业务请求单号{},转账失败,失败原因{}", businessPayRequest.getPaymentOrderCode(), e.getDisplayName(), e.getCause());
            PayResponseError.payEmphasisErrorBuild(businessPayResponse, e);
            businessPayResponse.setPayStatus(PaymentStatusEnum.PAYMENT_FAIL.getValue());
        } catch (Exception e) {
            log.error("微信转账到零钱查询，业务请求单号{},转账失败,失败原因{}", businessPayRequest.getPaymentOrderCode(), e.getMessage(), e.getCause());
            PayResponseError.payEmphasisErrorBuild(businessPayResponse, PayMsgEnum.PAYMENT_PURSE_FAIL);
            businessPayResponse.setPayStatus(PaymentStatusEnum.PAYMENT_FAIL.getValue());
        }
        return businessPayResponse;
    }

    /**
     * 异常填充查询明细数据
     * @param businessPayRequest
     * @param businessPayResponse
     * @param respJson
     */
    protected void exceptionFillPayPurseDetailData(BusinessPayPurseRequest businessPayRequest, BusinessPayPurseResponse businessPayResponse, JSONObject respJson) {
        List<BusinessPayeeDetail> businessPayeeDetail = businessPayRequest.getBusinessPayeeDetail();
        if (CollectionUtils.notEmpty(businessPayeeDetail)) {
            List<BusinessPayPurseDetail> payPurseDetails = businessPayeeDetail.stream().map(item -> {
                BusinessPayPurseDetail businessPayPurseDetail = new BusinessPayPurseDetail();
                businessPayPurseDetail.setPlatformPaymentOrderCode(item.getPlatformPaymentOrderCode());
                businessPayPurseDetail.setBusinessPaymentOrderCode(item.getBusinessPaymentOrderCode());
                businessPayPurseDetail.setPaymentStatus(WxPayPurseDetailStatusEnum.FAIL.getValue());
                businessPayPurseDetail.setPaymentExceptionCode(respJson.getString("code"));
                businessPayPurseDetail.setPaymentExceptionReason(respJson.getString("message"));
                return businessPayPurseDetail;
            }).collect(Collectors.toList());
            businessPayResponse.setPaymentResultDetail(payPurseDetails);
            //2022-07-11调整 end
        }
    }

    protected void processPaymentPurseExceptionHandle(BusinessPayPurseResponse businessPayResponse, JSONObject respJson) {
        businessPayResponse.setResultCode(respJson.getString("code"));
        businessPayResponse.setResultCodeDesc(respJson.getString("message"));
        businessPayResponse.setStatus(PaymentStatusEnum.PAYMENT_FAIL.getValue());
        businessPayResponse.setPayStatus(PaymentStatusEnum.PAYMENT_FAIL.getValue());
    }

    protected void processPaymentPurseResultHandle(BusinessPayPurseResponse businessPayResponse, BusinessPayPurseDetail businessPayPurseDetail) {
        //随机获取明细一条数据作主状态显示
        PaymentStatusEnum mainStatusEnum = PaymentStatusEnum.getEnumByValue(businessPayPurseDetail.getPaymentStatus());
        boolean isFail = ObjectUtils.isNull(mainStatusEnum) || Objects.equals(mainStatusEnum.getValue(), PaymentStatusEnum.PAYMENT_FAIL.getValue());
        businessPayResponse.setResultCode(isFail ? WXEmphasisConstants.FAIL_CODE : WXEmphasisConstants.SUCCESS_CODE);
        businessPayResponse.setResultCodeDesc(isFail ? PaymentStatusEnum.PAYMENT_FAIL.getName() : mainStatusEnum.getName());
        businessPayResponse.setStatus(isFail ? PaymentStatusEnum.PAYMENT_FAIL.getValue() : mainStatusEnum.getValue());
        businessPayResponse.setPayStatus(isFail ? PaymentStatusEnum.PAYMENT_FAIL.getValue() : mainStatusEnum.getValue());
    }

    protected void processPaymentPurseBatchStatusResultHandle(BusinessPayPurseResponse businessPayResponse, BusinessPayPurseDetail businessPayPurseDetail) {
        //随机获取明细一条数据作主状态显示
        businessPayResponse.setResultCode(businessPayPurseDetail.getPaymentExceptionCode());
        businessPayResponse.setResultCodeDesc(businessPayPurseDetail.getPaymentExceptionReason());
        businessPayResponse.setStatus(businessPayPurseDetail.getPaymentStatus());
        businessPayResponse.setPayStatus(businessPayPurseDetail.getPaymentStatus());
    }
    /**
     * 转账到零钱
     * @param response
     * @return
     */
    private static MessageData<AbstractEmphasisResponse> paymentPurseData(BusinessPayPurseResponse response) {
        try {
            if (null == response) {
                throw new IllegalArgumentException("转账到零钱响应参数为空！");
            }

            if (CollectionUtils.notEmpty(response.getPaymentResultDetail())) {
                response.getPaymentResultDetail().stream().forEach(item -> {
                    item.setPaymentStatus(response.getPayStatus());
                });
            }

            if (Objects.equals(response.getStatus(), PaymentStatusEnum.PAYMENT_ING.getValue()) || Objects.equals(response.getStatus(), PaymentStatusEnum.PAYMENT_SUCCESS.getValue())) {
                return MessageData.success(PayMsgEnum.OPERATION_SUCCESS, response);
            } else {
                return MessageData.build(true, false, response.getResultCode(), response.getResultCodeDesc(), response);
            }
        } catch (Exception e) {
            log.error("微信封装转账到零钱返回业务端数据异常,失败原因{}", e.getMessage(), e.getCause());
            return MessageData.fail(PayEmphasisMsgEnum.WX_PAY_QUERY_ERROR, response);
        }
    }
    @Override
    public <T extends AbstractOperationRequest> AbstractEmphasisResponse oauth(T request)  {
        ABstractOauthResponse response = new ABstractOauthResponse();
        try {
            PaySecondPhaseVO phaseVO = (PaySecondPhaseVO) request;
            PlatformChannelDTO platformChannel = phaseVO.getPlatformChannel();
            WeiXinEmphasisPayChannel weiXinEmphasisPayChannel = JSONObject.parseObject(platformChannel.getChannelConfigInfo(), WeiXinEmphasisPayChannel.class);
            //校验
            weiXinEmphasisPayChannel.validate(ChannelSourceEnum.SCAN_PAY);
            response.setCashRegisterUrl( String.format(WXEmphasisConstants.WECHAT_GET_CODE_URL,
                    weiXinEmphasisPayChannel.getPublicAppId(),
                    yunShanPayProperties.getWxRedirectUrl(),
                    Base64.encode(phaseVO.getRedisKey())));
        } catch (HCloudRuntimeException e) {
            log.error("微信静默授权组装转换异常,原因:{}",e.getDisplayName(), e.getMessage());
            throw new HCloudRuntimeException(e.getCode(), e.getDisplayName());
        } catch (Exception e) {
            log.error("微信静默授权组装数据异常,原因:{}", e.getMessage(), e.getCause());
            throw new HCloudRuntimeException(PayMsgEnum.QRCODE_OAUTH_ERROR);
        }
        return response;
    }
}
