package com.hydee.h3.pay.core.yunshanfu.internal.util;


import com.hydee.h3.pay.core.yunshanfu.OpenApiContext;
import com.hydee.h3.pay.core.yunshanfu.constants.ConfigBean;
import com.hydee.h3.pay.core.yunshanfu.internal.util.converter.Converter;
import com.hydee.h3.pay.core.yunshanfu.internal.util.converter.JsonConverter;
import com.hydee.h3.pay.core.yunshanfu.internal.util.http.HttpTransport;
import com.hydee.h3.pay.core.yunshanfu.request.TokenRequest;
import com.hydee.h3.pay.core.yunshanfu.response.TokenResponse;
import net.sf.json.JSONObject;
import org.apache.commons.lang3.StringUtils;

import java.security.MessageDigest;
import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 * User: ZHANGWEI
 * Date: 2016/12/5
 * Time: 15:41
 * 所属模块：
 * 功能说明：Open Token相关
 */
public class OpenTokenUtil {
    private static final String constant_classname=OpenTokenUtil.class.getSimpleName();

    /**
     * token sha256签名算法
     * @param appId
     * @param timestamp
     * @param nonce
     * @param appKey
     * @return
     */
    public static String generateSignature(String appId, String timestamp,
                                           String nonce, String appKey) {
        String plaintext = appId + timestamp + nonce + appKey;
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            digest.update(plaintext.getBytes());
            byte messageDigest[] = digest.digest();
            StringBuffer hexString = new StringBuffer();
            for (int i = 0; i < messageDigest.length; i++) {
                String shaHex = Integer.toHexString(messageDigest[i] & 0xFF);
                if (shaHex.length() < 2) {
                    hexString.append(0);
                }
                hexString.append(shaHex);
            }
            return hexString.toString();

        } catch (Exception e) {
            OpenApiLogger.logError(e.getCause());
        }
        return "";
    }



    /**
     * 获取token
     * @param context
     * @return
     */
    public static TokenResponse getToken(OpenApiContext context){
        int retry = 0;
        ConfigBean configBean = context.getConfigBean();
        TokenResponse acquiredToken = null;
        if (isTokenValid(context.getCurrentToken())
                && !isTokenTimeout(context.getCurrentToken())) {
            return context.getCurrentToken();
        }
        while (retry++ < configBean.getTokenAcquireReties()) {
            acquiredToken = acquireAccessToken(context);
            if (null != acquiredToken) {  //如果想根据请求token是否成功 用于重试 判断用if (null != acquiredToken.getAccessToken())
                break;
            } else if (retry < configBean.getTokenAcquireReties()) {
                try {
                    Thread.sleep(200);
                } catch (Exception e) {
                    OpenApiLogger.logError(constant_classname +" 重试token申请出错:"+e.getCause());
                }
            } else {
                OpenApiLogger.logError(constant_classname +" 申请token超过重试次数 bye-bye");
                break;
            }
        }
        return acquiredToken;
    }

    private static TokenResponse acquireAccessToken(OpenApiContext context){
        String appId = context.getAppId();
        String appKey = context.getAppKey();
        ConfigBean configBean = context.getConfigBean();
        TokenRequest tokenRequest = new TokenRequest(appId,appKey);
        JSONObject jsonObj = JSONObject.fromObject(tokenRequest);
        TokenResponse tokenResponse;
        String url = context.getOpenServUrl().concat(configBean.getVersion()).concat(configBean.getTokenServiceCode());
        try {
            String response = HttpTransport.getInstance().doPost(configBean.isProd(),url,null,jsonObj.toString());
            if(StringUtils.isBlank(response)) throw new Exception(constant_classname+"：服务提供方未返回");
            Converter converter = new JsonConverter();
            tokenResponse = converter.toResponse(response,TokenResponse.class);
            tokenResponse.setEffectTime(new Date());
            tokenResponse.setTimeout(tokenResponse.getExpiresIn());
            tokenResponse.setAheadInterval(configBean.getTokenAcquireAheadInterval());
            return tokenResponse;
        } catch (Exception e) {
            OpenApiLogger.logError(constant_classname +" exception:"+e.getCause());
        }
        return null;
    }

    private static boolean isTokenValid(TokenResponse tokenBean) {
        return (null != tokenBean
                && org.apache.commons.lang.StringUtils.isNotBlank(tokenBean.getAccessToken())
                && null != tokenBean.getEffectTime()
                && tokenBean.getTimeout() > 0
                && tokenBean.getAheadInterval() > 0 && tokenBean.getTimeout() > tokenBean
                .getAheadInterval());
    }

    private static boolean isTokenTimeout(TokenResponse tokenBean) {
        int elapseInterval = (int) ((new Date().getTime() - tokenBean
                .getEffectTime().getTime()) / 1000);
        int maxEffectiveInterval = tokenBean.getTimeout()
                - tokenBean.getAheadInterval();
        boolean isTimeout = (elapseInterval > maxEffectiveInterval);
        if (isTimeout) {
            OpenApiLogger.logError(constant_classname +" exception:token过期了");
        }
        return isTimeout;
    }

}
