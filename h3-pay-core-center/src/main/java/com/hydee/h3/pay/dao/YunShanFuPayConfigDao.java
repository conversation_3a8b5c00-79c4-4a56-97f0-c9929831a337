
package com.hydee.h3.pay.dao;

import com.hydee.h3.internal.api.paycore.bean.response.wx.PayWayRequestVO;
import com.hydee.h3.internal.api.paycore.bean.response.wx.PayWayResponseVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @author: 李婷
 * @className: PayWayMapper
 * @packageName: com.hydee.h3.finance.entity.contract.dao.transconfig
 * @description: 支付方式 Mapper层
 * @data: 2022-09-14
 **/
@Mapper
public interface PayWayDao {

    /**
     * 通过支持渠道查询支付方式
     *
     * @param payWayRequestVO 渠道 支付优先级
     * @param payChannelCodeList   支付通道编码集合
     * @return PayWayResponseVO
     */
    List<PayWayResponseVO> findPayWayByChannel(@Param("payWayRequestVO") PayWayRequestVO payWayRequestVO,
                                               @Param("payChannelCodeList") List<String> payChannelCodeList);
}
