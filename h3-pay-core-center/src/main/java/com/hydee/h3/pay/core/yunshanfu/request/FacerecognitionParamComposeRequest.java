package com.hydee.h3.pay.core.yunshanfu.request;

import com.hydee.h3.pay.core.yunshanfu.OpenApiRequest;
import com.hydee.h3.pay.core.yunshanfu.annotation.ApiField;
import com.hydee.h3.pay.core.yunshanfu.response.FacerecognitionParamComposeResponse;

/**
 * Created with IntelliJ IDEA.
 * User: ZHANGWEI
 * Date: 2017/6/5
 * Time: 14:33
 * 所属模块：
 * 功能说明：人脸识别参数拼装
 */
public class FacerecognitionParamComposeRequest implements OpenApiRequest<FacerecognitionParamComposeResponse> {
    @ApiField(key = "data",required = true,desc = "json格式字符")
    private Object data;
    public Class<FacerecognitionParamComposeResponse> responseClass() {
        return FacerecognitionParamComposeResponse.class;
    }

    public String apiVersion() {
        return "v1";
    }

    public String apiMethodName() {
        return "人脸识别参数拼装";
    }

    public String serviceCode() {
        return "/datacenter/smartverification/facerecognition/param/compose";
    }

    public boolean needToken() {
        return true;
    }
    public Object getData() {
        return data;
    }

    public void setData(Object data) {
        this.data = data;
    }
}
