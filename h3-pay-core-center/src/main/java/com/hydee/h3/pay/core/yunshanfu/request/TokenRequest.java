package com.hydee.h3.pay.core.yunshanfu.request;

import com.hydee.h3.pay.core.yunshanfu.OpenApiRequest;
import com.hydee.h3.pay.core.yunshanfu.annotation.ApiField;
import com.hydee.h3.pay.core.yunshanfu.internal.util.OpenTokenUtil;
import com.hydee.h3.pay.core.yunshanfu.response.TokenResponse;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.UUID;

/**
 * Created with IntelliJ IDEA.
 * User: ZHANGWEI
 * Date: 2016/12/5
 * Time: 15:42
 * 所属模块：
 * 功能说明：token
 */
public class TokenRequest implements OpenApiRequest<TokenResponse> {
    @ApiField(name = "appId",required = true ,desc = "开放平台开发者id")
    private String appId;
    @ApiField(name = "appKey",required = true,desc = "开放平台开发者key")
    private String appKey;
    @ApiField(name = "时间戳",required = true)
    private String timestamp;
    @ApiField(name = "随机数",required = true)
    private String nonce;
    @ApiField(name = "加密方法",required = true)
    private String signMethod;
    @ApiField(name = "SHA256签名字符",required = true)
    private String signature;

    public Class<TokenResponse> responseClass() {
        return TokenResponse.class;
    }

    public String apiVersion() {
        return "v2";
    }   //新入驻商户需要使用v2方式

    public String apiMethodName() {
        return "获取开放平台token";
    }

    public String serviceCode() {
        return "/token/access";
    }

    public boolean needToken() {
        return false;
    }

    public TokenRequest(String appId, String appKey) {
        this.appId = appId;
        this.appKey = appKey;
        this.timestamp = new SimpleDateFormat("yyyyMMddHHmmss")
                .format(new Date());
        this.nonce = UUID.randomUUID().toString();
        this.signMethod = "SHA256";
        this.signature =  OpenTokenUtil.generateSignature(getAppId(),getTimestamp(),getNonce(),getAppKey());
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getAppKey() {
        return appKey;
    }

    public void setAppKey(String appKey) {
        this.appKey = appKey;
    }

    public String getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(String timestamp) {
        this.timestamp = timestamp;
    }

    public String getNonce() {
        return nonce;
    }

    public void setNonce(String nonce) {
        this.nonce = nonce;
    }

    public String getSignMethod() {
        return signMethod;
    }

    public void setSignMethod(String signMethod) {
        this.signMethod = signMethod;
    }

    public String getSignature() {
        return signature;
    }

    public void setSignature(String signature) {
        this.signature = signature;
    }
}
