package com.hydee.h3.pay.common;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * @version: V1.0
 * @author: minchen
 * @className: WxPaymentProperties
 * @packageName: com.hydee.h3.pay.common
 * @description: 阿波罗配置
 * @data: 2021/11/1 11:24
 **/
@Component
@ConfigurationProperties(prefix = "yun.payment.nr")
@Data
public class YunShanPayProperties {

    private String plantNotifyUrl;
    private String privateKey;
    private String merCode;
    private String connectTimeout;
    private String readTimeout;
    private String plantRefundNotifyUrl;
    private String yantaiNotifyUrl;
    private String yantaiRefundNotifyUrl;
    private String insuranceNotifyUrl;
    private String wxRedirectUrl;


}
