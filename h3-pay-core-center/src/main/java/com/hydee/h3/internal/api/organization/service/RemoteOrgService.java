//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.hydee.h3.internal.api.organization.service;

import com.hydee.common.beans.message.MessageData;
import com.hydee.common.beans.message.MessageQuery;
import com.hydee.h3.internal.api.organization.bean.PagePARA;
import com.hydee.h3.internal.api.organization.bean.QueryUserByCodeVO;
import com.hydee.h3.internal.api.organization.bean.org.CompanyCodeVO;
import com.hydee.h3.internal.api.organization.bean.org.CompanyOrgDTO;
import com.hydee.h3.internal.api.organization.bean.org.CompanyOrgVo;
import com.hydee.h3.internal.api.organization.bean.org.EnabledOrgLicense;
import com.hydee.h3.internal.api.organization.bean.org.EnabledOrgLicenseForCompany;
import com.hydee.h3.internal.api.organization.bean.org.FindAdminVO;
import com.hydee.h3.internal.api.organization.bean.org.FindOrgTreeRspVO;
import com.hydee.h3.internal.api.organization.bean.org.FindUserOrgRspVO;
import com.hydee.h3.internal.api.organization.bean.org.GroupIdsVO;
import com.hydee.h3.internal.api.organization.bean.org.LicenceEffectRespVO;
import com.hydee.h3.internal.api.organization.bean.org.OrgAdminVO;
import com.hydee.h3.internal.api.organization.bean.org.OrgBaseLoginVO;
import com.hydee.h3.internal.api.organization.bean.org.OrgBaseRespVO;
import com.hydee.h3.internal.api.organization.bean.org.OrgBusinessLicenseDTO;
import com.hydee.h3.internal.api.organization.bean.org.OrgBusinessStoreDTO;
import com.hydee.h3.internal.api.organization.bean.org.OrgClassRespVO;
import com.hydee.h3.internal.api.organization.bean.org.OrgCodeQueryReqVO;
import com.hydee.h3.internal.api.organization.bean.org.OrgCodeQueryRespVO;
import com.hydee.h3.internal.api.organization.bean.org.OrgCompanyResultVO;
import com.hydee.h3.internal.api.organization.bean.org.OrgOldCodeReqVO;
import com.hydee.h3.internal.api.organization.bean.org.OrgOldCodeRespVO;
import com.hydee.h3.internal.api.organization.bean.org.OrgPayTaxReqVO;
import com.hydee.h3.internal.api.organization.bean.org.OrgPayTaxRespVO;
import com.hydee.h3.internal.api.organization.bean.org.OrgRespVO;
import com.hydee.h3.internal.api.organization.bean.org.OrgUserRespVO;
import com.hydee.h3.internal.api.organization.bean.org.OrganizationBaseInfo;
import com.hydee.h3.internal.api.organization.bean.org.OrganizationDetailsVO;
import com.hydee.h3.internal.api.organization.bean.org.OrganizationInfo;
import com.hydee.h3.internal.api.organization.bean.org.OrganizationOrgInfo;
import com.hydee.h3.internal.api.organization.bean.org.PageMerchantStoreVO;
import com.hydee.h3.internal.api.organization.bean.org.StoreInfoVO;
import com.hydee.h3.internal.api.organization.bean.org.TopCustomerResp;
import com.hydee.h3.internal.api.organization.bean.role.FindUserRoleRspVO;
import com.hydee.h3.internal.api.organization.bean.user.reqvo.OrgQueryReqVO;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(value = "h3-orgmanager-v2",url = "http://h3-orgmanager-center.svc.k8s.test.hxyxt.com/")
public interface RemoteOrgService {
    @GetMapping({"/private/org/for-user/{userId}"})
    MessageData<List<OrganizationBaseInfo>> findUserForOrg(@PathVariable("userId") Long var1);

    @PostMapping({"/private/org/scope/list"})
    MessageData<List<OrganizationBaseInfo>> getOrgListByScope(@RequestParam("businessScope") String var1);

    @PostMapping({"/private/org/list/codes"})
    MessageData<List<OrganizationOrgInfo>> getOrgListByCode(@RequestParam("orgCodes") Set<String> var1);

    @PostMapping({"/private/org/by/code"})
    MessageData<OrganizationOrgInfo> getOrByCode(@RequestParam("orgCode") String var1);

    @GetMapping({"/private/org/companys"})
    MessageData<List<OrganizationBaseInfo>> getOrgListByGroupId(@RequestParam(value = "entityOrg",required = false) Integer var1);

    @GetMapping({"/private/org/upward-company/{orgId}"})
    MessageData<OrganizationBaseInfo> upwardCompanyFindByOrgId(@PathVariable("orgId") Long var1, @RequestParam(value = "entityOrg",required = false) Integer var2);

    @GetMapping({"/private/org/for-group"})
    MessageData<List<OrganizationBaseInfo>> findOrgByGroupId(@RequestParam(value = "groupId",required = false) Long var1, @RequestParam(value = "businessType",required = false) Integer var2);

    @GetMapping({"/private/org/for-company/{companyId}"})
    MessageData<List<OrganizationBaseInfo>> findOrgByCompanyId(@PathVariable("companyId") Long var1, @RequestParam(value = "businessType",required = false) Integer var2);

    @GetMapping({"/private/org/for_belong_company_org/{companyId}"})
    MessageData<List<OrganizationBaseInfo>> findOrgByBelongCompanyOrg(@PathVariable("companyId") Long var1, @RequestParam(value = "businessType",required = false) Integer var2);

    @GetMapping({"/private/org/query/id/{orgId}"})
    MessageData<OrganizationBaseInfo> findOrgByOrgId(@PathVariable("orgId") Long var1);

    @GetMapping({"/private/org/query/no_group/id/{orgId}"})
    MessageData<OrganizationBaseInfo> findOrgById(@PathVariable("orgId") Long var1);

    /** @deprecated */
    @GetMapping({"/private/org/query/ids"})
    @Deprecated
    MessageData<List<OrganizationBaseInfo>> findOrgByOrgIds(@RequestParam("ids") List<Long> var1);

    /** @deprecated */
    @GetMapping({"/private/org/query/ids_group"})
    @Deprecated
    MessageData<List<OrganizationBaseInfo>> findOrgByOrgIdsAndGroupId(@RequestParam("ids") List<Long> var1, @RequestParam("groupId") Long var2);

    @PostMapping({"/private/org/query/post/ids"})
    MessageData<List<OrganizationBaseInfo>> findOrgPostByOrgIds(@RequestBody List<Long> var1);

    @PostMapping({"/private/org/query/post/ids/no/group"})
    MessageData<List<OrganizationBaseInfo>> findOrgPostByOrgIdsNoGroup(@RequestBody List<Long> var1);

    @PostMapping({"/private/org/list"})
    MessageData<List<OrganizationInfo>> getOrgByOrgIds(@RequestParam("orgIds") List<Long> var1);

    /** @deprecated */
    @GetMapping({"/private/org/for-user/{userId}/orgs"})
    @Deprecated
    MessageData<List<OrganizationBaseInfo>> findOrgByUserId(@PathVariable("userId") Long var1, @RequestParam(value = "enterprises",required = false) Integer var2, @RequestParam(value = "businessType",required = false) Integer var3);

    @GetMapping({"/private/org/for-user/{userId}/orgs/batch"})
    MessageData<List<OrganizationBaseInfo>> findOrgByUserIdAndBusiTypes(@PathVariable("userId") Long var1, @RequestParam(value = "enterprises",required = false) Integer var2, @RequestParam(value = "businessType",required = false) List<Integer> var3);

    @PostMapping({"/private/org/for-user/{userId}/login/page/orgs"})
    MessageQuery<OrgBaseLoginVO> findLoginOrgPageForUserId(@PathVariable("userId") Long var1, @RequestParam(value = "enterprises",required = false) Integer var2, @RequestParam(value = "orgName",required = false) String var3, PagePARA var4);

    @PostMapping({"/private/org/for-user/{userId}/login/orgs"})
    MessageData<List<OrganizationBaseInfo>> findLoginOrgForUserId(@PathVariable("userId") Long var1, @RequestParam(value = "enterprises",required = false) Integer var2);

    @PostMapping({"/private/org/{orgId}/for-user/{userId}/login"})
    MessageData<Boolean> checkCompanyLoginOrgForUserId(@PathVariable("userId") Long var1, @PathVariable("orgId") Long var2);

    @GetMapping({"/private/org/license/{orgId}"})
    MessageData<List<OrgBusinessLicenseDTO>> findOrgLicenseByOrgId(@PathVariable("orgId") Long var1);

    @GetMapping({"/private/org/license/ids"})
    MessageData<List<OrgBusinessLicenseDTO>> findOrgLicenseByOrgIds(@RequestParam("ids") List<Long> var1);

    @GetMapping({"/private/org/group"})
    MessageData<List<OrganizationBaseInfo>> findGroupInfo(@RequestParam(value = "name",required = false) String var1);

    @GetMapping({"/private/org/query_by_class"})
    MessageData<List<OrgClassRespVO>> queryOrgByClassId(@RequestParam("classIdList") List<Long> var1);

    @GetMapping({"/private/org/{orgId}/store"})
    MessageData<OrgBusinessStoreDTO> findOrgStoreByOrgId(@PathVariable("orgId") Long var1);

    @GetMapping({"/private/org/store_group"})
    MessageData<OrgBusinessStoreDTO> findOrgStoreByOrgIdAndGroupId(@RequestParam("orgId") Long var1, @RequestParam("groupId") Long var2);

    @GetMapping({"/private/org/store"})
    MessageData<Map<Long, OrganizationBaseInfo>> findUpWarehouse(@RequestParam("orgIds") List<Long> var1);

    @GetMapping({"/private/org/for_companys"})
    MessageData<List<OrganizationBaseInfo>> getOrgListForGroupId(@RequestParam("groupId") Long var1);

    @GetMapping({"/private/org/stores"})
    MessageData<Map<Long, OrgBusinessStoreDTO>> findOrgStoreByOrgIds(@RequestParam("orgIds") List<Long> var1);

    @PostMapping({"/private/org/update_org_license"})
    MessageData<Void> enabledOrgLicense(@RequestBody EnabledOrgLicense var1);

    @PostMapping({"/private/org/update_company_license"})
    MessageData<Void> enabledOrgLicenseForCompany(@RequestParam("licenseId") Long var1, @RequestBody List<EnabledOrgLicenseForCompany> var2);

    @GetMapping({"/private/org/find_send_warehouses"})
    MessageData<Map<Long, Long>> findWarehouses(@RequestParam("businessIds") List<Long> var1);

    @PostMapping({"/private/org/find_org_by_old_code"})
    MessageData<List<OrgOldCodeRespVO>> findOrgByOldCode(@RequestBody OrgOldCodeReqVO var1);

    @GetMapping({"/private/org/find_department_by_company_id"})
    MessageData<List<OrganizationBaseInfo>> findDepartmentByCompanyId(@RequestParam("companyId") Long var1);

    @GetMapping({"/private/org/find_all_deparament_by_company_id"})
    MessageData<List<OrganizationBaseInfo>> findAllDepartmentByCompanyId(@RequestParam("companyId") Long var1);

    @GetMapping({"/private/org/details"})
    MessageData<OrganizationDetailsVO> findOrgDetailsById(@RequestParam("id") Long var1);

    @GetMapping({"/private/org/details/group"})
    MessageData<OrganizationDetailsVO> findOrgDetailsByIdGroup(@RequestParam("id") Long var1, @RequestParam("groupId") Long var2);

    @PostMapping({"/private/org/paytax"})
    MessageData<List<OrgPayTaxRespVO>> findOrgPaytaxList(@RequestBody OrgPayTaxReqVO var1);

    @GetMapping({"/private/org/query_licence_effect"})
    MessageData<List<LicenceEffectRespVO>> queryLicenceEffect(@RequestParam("groupId") Long var1, @RequestParam("companyId") Long var2, @RequestParam("effectDays") Integer var3);

    @PostMapping({"/private/org/query_by_codes"})
    MessageData<List<OrgCodeQueryRespVO>> queryByCodes(@RequestBody OrgCodeQueryReqVO var1);

    @PostMapping({"/private/org/query_by_belong_entity"})
    MessageData<List<OrganizationBaseInfo>> findGroupOrgPage(@RequestBody OrganizationInfo var1);

    @PostMapping({"/private/org/group/page/orgs"})
    MessageData<List<OrganizationBaseInfo>> findGroupOrgPage(@RequestParam("groupId") Long var1, @RequestParam("id") Long var2, @RequestParam(value = "pageSize",required = false,defaultValue = "200") Integer var3, @RequestParam(value = "companyIds",required = false) List<Long> var4);

    @PostMapping({"/private/org/getUserOrgsBatch"})
    MessageData<List<OrgUserRespVO>> getUserOrgsBatch(@RequestParam("groupId") Long var1, @RequestParam("companyId") Long var2, @RequestParam("userIds") List<Long> var3);

    @PostMapping({"/private/org/get_company_by_group_ids"})
    MessageData<List<OrgCompanyResultVO>> getCompanyByGroupIds(@RequestBody GroupIdsVO var1);

    @PostMapping({"/private/org/find_admin"})
    MessageData<List<OrgAdminVO>> findAdminByGroupIdAndCompanyId(@RequestBody FindAdminVO var1);

    @PostMapping({"/private/org/find_org_by_org_codes"})
    MessageData<List<OrganizationBaseInfo>> findOrgByOrgCodes(@RequestBody OrgQueryReqVO var1);

    @PostMapping({"/private/org/find_weak_controller"})
    MessageData<TopCustomerResp> findWeakController(@RequestParam("userId") Long var1, @RequestParam("groupId") Long var2);

    @PostMapping({"/private/org/find_org_tree"})
    MessageData<List<FindOrgTreeRspVO>> findOrgTree(@RequestParam(value = "merCode",required = false) String var1, @RequestParam(value = "groupId",required = false) Long var2, @RequestParam(value = "orgParentId",required = false) Long var3);

    @PostMapping({"/private/org/find_user_org"})
    MessageData<List<FindUserOrgRspVO>> findUserOrg(@RequestBody QueryUserByCodeVO var1);

    @PostMapping({"/private/org/find_user_role"})
    MessageData<List<FindUserRoleRspVO>> findUserRole(@RequestBody QueryUserByCodeVO var1);

    @PostMapping({"/private/org/page_merchant_store"})
    MessageData<List<StoreInfoVO>> pageMerchantStore(@RequestBody PageMerchantStoreVO var1);

    @PostMapping({"/private/org/find_group_id_by_code"})
    MessageData<OrganizationBaseInfo> findGroupIdByCustomerCode(@RequestParam("customerCode") String var1);

    @PostMapping({"/private/org/find_company_id_by_code"})
    MessageData<List<OrganizationBaseInfo>> findErpCompanyIdByCode(@RequestBody CompanyCodeVO var1);

    @PostMapping({"/private/org/query_company_org"})
    MessageData<List<CompanyOrgVo>> queryOrgUnderTheCompany(@RequestBody CompanyOrgDTO var1);

    @GetMapping({"/private/org/find_department_by_staff_id"})
    MessageData<OrgRespVO> findDepartmentByStaffId(@RequestParam("staffId") Long var1, @RequestParam("groupId") Long var2);

    @PostMapping({"/private/org/query_org_base_info_by_group"})
    MessageData<List<OrgBaseRespVO>> queryOrgBaseInfoByGroup(@RequestBody CompanyOrgDTO var1);
}
