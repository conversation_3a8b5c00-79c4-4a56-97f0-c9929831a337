server.port = 10133
spring.application.name = h3-pay-core-jyb
spring.datasource.driver-class-name = com.mysql.jdbc.Driver
spring.datasource.url = ********************************************************************************************************************
spring.profiles.active = test
purchase.overdue.pagesize = 10

mybatis.mapper-locations = classpath:mapper/*.xml
mybatis.type-aliases-package = com.hydee.h3.pay.entity
mybatis.configuration.map-underscore-to-camel-case = true
logging.level.com.hydee.h3.purchase.* = debug
mybatis.type-handlers-package = com.hydee.h3.pay.mybatis


pingan.base-url = https://openapi.test.pajk.cn/api/v1/
pingan.pay-api-id = c08606c193f3f12962ecf575069ebcd5#TEST
pingan.refund-api-id = e56f5c5feebed6760966e79014a36480#TEST

tonglian.pay-url = https://vsp.allinpay.com/apiweb/unitorder/scanqrpay
tonglian.query-url = https://vsp.allinpay.com/apiweb/tranx/query
tonglian.refund-url = https://vsp.allinpay.com/apiweb/tranx/refund

wx.payment.nr.plantNotifyUrl = https://dev-hcloud.hxyxt.com/h3-pay-core/public/secondPhase/pay/callback/v1.1
wx.payment.nr.merCode = 999999
wx.payment.nr.privateKey = MIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQCiFl9l3+AQR5+JO6yEjJMxv3qDufKYvnj6/G97Ao6vzsAJSpb4KHbWVMqhX0ltu7z/uHt04lw666JvCUEI/sly+9wZaWNESLL6Z9TbOkdDVI4XlzolrjKhY2lJEjTr7vtD8wtWmMY6Q2dBPa/rO9uYY8YUhMvQih2OIglHHXOlkhUKkfkHp5qW1ODrO0XrNXZD2tCZecVvLUkHCox0uxz323s77XSXH+1dJ4yn09oFeLxLOSSHk04j/mUsypYz/SHfgCAeSH0ZPtAWSvYkpyppWtuPFJBZz7A5x15MkfMA252y6WiNh66WF1FRGaGiXbII4tPrlz8WsdR/IRJ3YGypAgMBAAECggEAPnawvOnRx3RSnuhIS6yA7SMFa841F3gP12mA7L7Hjij3CJ2eIHQ3l9GFZTr7aTZ4V3QY36p8MJiuOGwwi+7kY1N8Nq7OenopvyZUeUe0PtZPC/hTiEHhcSCGaoqBqoqmekEnlW+6tKlVGWdUbCrdLZUFuNKUWQFg8m1bdfR7pMpSCtkLN+8P2JGvV65SwnJ6h9OyTW5GiKphR1U2A9PSdZ06vu6NVGCpMU9dGbPpzF7PZY9YoSsWKmWsA4uihOrzUim29AEgZhJL+FRJnERJKbBByamkySgCXFKwln5dez4yiNa++qbkPvorsHnEU+HSz9dKVLvNX4AIOCPTNqtqoQKBgQDShaeAYU9k6QLIWaQ//twDRmhbLuk6K/xCQe3Z+C+Y52wJT8SGU1yC+PIOhDfkD0uyHm5guTQ3qghDEWVmGOEHKL8+wqnKkapVr4TLAky5GSw+ddQZn2qjaUEXNSGww5e61pF7MPLnFUTQ+/9mAVqU8pMxzGBtwc9QEIw7PcfjqwKBgQDFGiuv3KbJq9vooYHVgUILfwvb0jD67wLd4ihR5fFhv6xW9WQVPTcntz02XSwvC71XU/iZT1juhlRLXoqCPn2SWbFMvmLjwle4ptURv105yY8TOg7vCWiVFRM+KOImP0FQV2ywH/Kcd1nQGQpz7dKin/rfYomWwFrbcHyPmPic+wKBgBKcolxlPgGr4AUad7z4qTbY6Kz3DsOH0/t/nKmimYKpIxIHrVY61Hj3zV6fLdnJyGVs71E5znYFYEmdphtKo8pgPdgsmZpBGpAMvm68O1A5+4FiJWhGgcUEry6AEgRwpl9sTNxZTYBEjxryzHeWMw3sDDtg2D2b3tozPFKYea2VAoGAILZoO8bWU7BXTjPH9aR/TqMmV7R59pvGkQbhBD6yojOKqeAOXUwMpGXQHf62v5QmN6EzKFa3/taClQO630T52FlUiNUsWnq9zmK6CDkHgvjF6z3+joO0PNIDoJRufESRVYE9A7o5rOhOGrCdQIJfLCh7HaogBcolCjiQI9ESqhMCgYA40hnHgn9993f4VaXNaQG+3p5sokZhCgr6eeKPTygWn9BdQbVUevvJSgrm9A59asUXAkOkdSTq6/SE2ePap+J4Hp0HgqhlBNbLbC9boc1yOflc+Fwd6rRndDCJTbab+7Mqc7ks1K7E669JHoBqA6lzoGpBEbr/OFqHmaFsihkjoQ1==
wx.payment.nr.connectTimeout = 12000
wx.payment.nr.readTimeout = 12000
wx.payment.nr.plantRefundNotifyUrl = https://dev-hcloud.hxyxt.com/h3-pay-core/public/secondPhase/refund/callback/v1.1
redis.redisson.cluster.enabled = true
redis.redisson.cluster.address = redis://**********:9000,redis://**********:9001,redis://**********:9002,redis://**********:9003
wx.payment.nr.yantaiRefundNotifyUrl = https://paycenter-test.haidianyun.com/public/yantai/pay/callback/v1.1
wx.payment.nr.yantaiNotifyUrl = https://paycenter-test.haidianyun.com/public/yantai/refund/callback/v1.1
common.payment.conf.cashRegisterUrl = https://open-test.hcloud.hydee.cn/pay-h5/
common.payment.conf.getCodeRedirectUri = https://paycenter-test.haidianyun.com/public/qrcode/wecatRedirect
common.payment.conf.appsecret = 8509e16aba88bf42725ebe7e2525aea1
common.payment.conf.appid = wxc112b1e9cca5bc59
common.payment.conf.qrCodeUrl = https://paycenter-test.haidianyun.com/public/qrcode/qrCodeUrl
wx.common.service.applet.mchid = 1272805201
wx.common.service.applet.secretKey = BL8ACIGPJAqhDKEyxcBLshgV782BJaMo
wx.common.service.applet.publicKey = HYDEEsoft2015weixinzhifumiyao123
wx.common.service.applet.privateKey = MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQCnInxy8Ya8EegsgNb8envFz5/be7NPMnQCOCAhlKqzO7vp1FXc2uansM64GdY997KHo0d+FvPG5+IMy2UkMtiqkfCCg6m0Qqi1XuH/Ba2zp9oAnOovNCbltPB4lttmU1J3IezYPgNq+iBEwMtnMSMmYEPFOuRPVPuR6BmA1b2WafmRXiV5ho4dOtuu+kU6y3daTvaRScKm2HYDfnpUyx1OGLLneuurJrF+Cr7Fap1fgZh/Km09UlSn5EvN+eG0GClpOkc1mlnroO75CUjbhTt6TdtT7N5gPgbMuWWqTElj5aSToUMPKn7XYUnPzCqxbhV9rNwDGTL9/lnX4ARJNLHLAgMBAAECggEBAJrAMqyW96LhjbDPJsAlhL72bFRj5/W3iHSZlR6isZ88+tQZ99qDkLfJ3M3XlmhQFazDQSn9hIAyKjqghSiML9NXR3MBB7vRULaGgQiJMHk9PHv+zlMgnbdo3pM5UzIeiiemcM8UNEP0Dra6MFt+wsYAsh9HVSgjTr+dQSfqQB2oyHJV/6cEgza6ICQnZsIev0MFnQknp/ISLR1lbCFQ4Fpuhj2xi1Uc9zRfS0uJgvRg58IRDhj45TtpFVdOHp3DIbD8yCkABBCwiRft/oeqRG776+0m4m8fwLlvnig+dUT4tzfFAAK7bnrn/u1ufG6HYuhEdIQpjknASqq63umlLwECgYEA2uaz5ZucwBmLMzopqKxEz2/maHCZT4EfKw85mXo8KwtprIlvBOQffqutJAKtH9eiq4RLZeBFHwRHnnUor/sOHaeXxe7c0exG19UElCawrNbQxIL4PuBlzq2zbFMH11bD+1/Qz5hIwO62w6Rt/UA/MrWAX62083LogFkkefuvxUsCgYEAw3XU0j7z+9M9jZtWqeyCin6tl1Nbr4Gzgg/Ng5pU44ozdlmPARh9149BXp9md6mylfE+pqkQz77qJuSGQrIKlYHyLz0hFOOi3YXWxROhphGVsHFjZKbLvIog3KzCJ5eYo2ZHRzkfBM3YZUg9Tz4yGUcEtj8VFcarQ7UBRRIFdYECgYBNyNz0Ii+5M1p7gZd8EHsDG0hMpHZWAhQJQQe+uejXgt6yKMQCgO2QHYEQ4sKSJhXvzLreIKtvMh4g+JDUt+l0MDGGbDKbhdJGr7NI0dbGFC9rJP8BmMuUfbTcMUUACBzsHkgc/IgdxrW7H5rV+xZ59nT/isJaH+LueL61efIjsQKBgQCsKVRsX5DN5dFYslNSVoTg8hV6raybWRXA4KR3X2+X//m2K8o+/9CQFplQe/xoOCuZ0KULbOzm31UlLrJneqTB7XRhUWmbGaoxjpqOee0u9lwvzp/qXtQjXFL3rJ9S+j8y5Xqw7Eo7FAifpVKDQtgj1Z7723J5/MSLXZohuW7fgQKBgBZOVq3aUSfX11bUsVs1UOpbVzxx3m25gRWnxg2bUumMneiWQ6kRMV6od9ykt3OA+38Bdt7U0VgMSeqcbhhil/XvNmqF8/qoIqG07VXuohuFbgGCGr5HO9Q3NSgGxSVBX+h5NjrLKXvnERrp8fOkpEbdnmlP12vbrYSL4vFPewei1
wx.common.service.applet.customerSerialNumber = 8509e16aba88bf42725ebe7e2525aea1
ali.payment.nr.plantNotifyUrl = https://paycenter-test.haidianyun.com/public/secondPhase/alipay/callback/v1.1
ali.payment.nr.plantRefundNotifyUrl = https://paycenter-test.haidianyun.com/public/secondPhase/alirefund/callback/v1.1
redis.redisson.cluster.password = yxt_redis123
pay.exception.order.max = 2
huifu.payment.plantPayNotifyUrl = https://paycenter-test.haidianyun.com/public/secondPhase/huifupay/callback/v1.1
huifu.payment.plantRefundNotifyUrl = https://paycenter-test.haidianyun.com/public/secondPhase/huifurefund/callback/v1.1
huifu.payment.wxRedirectUrl = https://paycenter-test.haidianyun.com/public/qrcode/weCatCallback
tonglian.config.split.data = 2022-05-31
yinlianshangwu.pay-url = https://api-mop.chinaums.com/v4/poslink/transaction/pay
yinlianshangwu.refund-url = https://api-mop.chinaums.com/v2/poslink/transaction/refund
yinlianshangwu.query-url = https://api-mop.chinaums.com/v2/poslink/transaction/query
yinlianshangwu.query-refund-url = https://api-mop.chinaums.com/v2/poslink/transaction/query-refund
common.ticket.url = https://open-test.hcloud.hydee.cn/pay-h5/#/pages/ticket/index?sub_mch_id=%s&out_trade_no=%s&transaction_id=%s
pingan.data-json = {\n"apiUrl":"https://opengw.test.pajk.cn/api/v1/",\n"reverseScan":"c08606c193f3f12962ecf575069ebcd5#TEST",\n"orderCancel":"e56f5c5feebed6760966e79014a36480#TEST",\n"quoteWithToken":"0d1578919010b2701a380cba222afa11#TEST",\n"prePayQuery":"70e34466e3da1623a03e60a688d8065e#TEST",\n"prePayScan":"8408863aee7fe0104e95669d1fe72fe3#TEST",\n"payCancel":"0558752788129844801c40cb8c16b73c#TEST",\n"preCalc":"9c195789802c9054c4027bea3823fe26#TEST",\n"queryOrderStatus":"df65b840286a851743f5725a1184a160#TEST",\n"queryBalance":"2377950aa48c4d3cae76a9e4044244cc#TEST",\n"queryPayChannel":"dbe5048faae3f6f233f6569db71fb0c9#TEST"\n}
yibaopay.query-url = https://cash-test3.ebaolife.net/interface/detailBySerial
yibaopay.refund-url = https://cash-test3.ebaolife.net/interface/cancel?v=2
area.payment.plantPayNotifyUrl = http://open-test.hcloud.hydee.cn/gateway/open-pay-core/callback/pay/unification/v1.1
area.payment.plantRefundNotifyUrl = http://open-test.hcloud.hydee.cn/gateway/open-pay-core/callback/refund/unification/v1.1
area.payment.plantPayJsonNotifyUrl = https://paycenter-test.haidianyun.com/open-api/callback/pay-json/unification/v1.1
area.payment.plantRefundJsonNotifyUrl = https://paycenter-test.haidianyun.com/open-api/callback/refund-json/unification/v1.1
area.payment.plantPayXmlNotifyUrl = https://paycenter-test.haidianyun.com/open-api/callback/pay-xml/unification/v1.1
area.payment.plantRefundXmlNotifyUrl = https://paycenter-test.haidianyun.com/open-api/callback/refund-xml/unification/v1.1
yibaopay.plant-notify-url = https://paycenter-test.haidianyun.com/public/secondPhase/yibaopay/callback/v1.1
common.payment.conf.mallRegisterUrl = https://m.tjg-test.ydjia.cn/pay-h5/
common.payment.conf.fixedQrCodeRedirectUri = https://paycenter-test.haidianyun.com/public/qrcode/fixedQrCodeRedirect
youhuifu.pay-url = https://nap.psbc.com/trans/intermgr/online/api/platform/qrserviceNet/orderPay
youhuifu.query-url = https://nap.psbc.com/trans/intermgr/online/api/platform/qrserviceNet/orderQuery
youhuifu.refund-url = https://nap.psbc.com/trans/intermgr/online/api/platform/qrserviceNet/orderRefund
youhuifu.query-refund-url = https://nap.psbc.com/trans/intermgr/online/api/platform/qrserviceNet/refundQuery
cloud.domain = https://api-service.yunzhanghu.com
cloud.plantNotifyUrl = https://paycenter-test.haidianyun.com/public/secondPhase/cloudPurse/callback/v1.1
cloud.tripleDeskey = YfUHcT318hyZhu2LuVa7Ws3W

# rocketmq?????
rocketmq.name-server = **********:9876
rocketmq.producer.group = PGROUP_${spring.application.name}_${spring.profiles.active}
rocketmq.producer.customized-trace-topic = TOPIC_MIDDLE_ORDER_DATA_TEST
rocketmq.producer.send-message-timeout = 300000
rocketmq.producer.compress-message-body-threshold = 4096
rocketmq.producer.max-message-size = 4194304
rocketmq.producer.retry-times-when-send-failed = 2
rocketmq.producer.retry-times-when-send-async-failed = 0
rocketmq.producer.retry-next-server = true
guobiaoyibao.getUserUrl = https://med-biz-pre.wecity.qq.com/api/mipuserquery/userQuery/
wx.payment.nr.insuranceNotifyUrl = https://open-test.hcloud.hydee.cn/gateway/open-pay-core/callback/payInsurance/unification/v1.1
spring.datasource.username = agent
spring.datasource.password = WrHNOhOGHR8yzMEgKvao
huifu.payment.huifuId = 6666000142524776
huifu.payment.acctWarningMoney =

xxl.job.admin.addresses = https://test-xxl-job-new.hxyxt.com/xxl-job-admin/
xxl.job.executor.logpath = /data/k8s/logs/applogs/xxl-job/jobhandler/
xxl.job.executor.logretentiondays = -1

alarm.sendErrorLog.enable = false



###########################
eureka.instance.prefer-ip-address = true
eureka.instance.lease-renewal-interval-in-seconds = 5
eureka.instance.lease-expiration-duration-in-seconds = 20
eureka.client.fetch-registry = true
eureka.client.service-url.defaultZone = http://************:8083/eureka
#redis.pattern = standalone
redis.pattern = cluster
ribbon.ReadTimeout = 60000
ribbon.ConnectTimeout = 60000
feign.hystrix.enabled = true
hystrix.command.default.execution.isolation.strategy = SEMAPHORE
hystrix.command.default.execution.isolation.thread.timeoutInMilliseconds = 30000
#hystrix.command.default.execution.isolation.strategy = THREAD

custom.rest.connection.connectionRequestTimeout = 30000
custom.rest.connection.connectTimeout = 30000
# ????
logs.dir = ${user.dir}/logs
logs.info.file = ${logs.dir}/${spring.application.name}.info.log
logs.info.max-history = 7
logs.error.file = ${logs.dir}/${spring.application.name}.error.log
logs.error.max-history = 7
logs.audit.file = ${logs.dir}/${spring.application.name}.audit.log
logs.audit.max-history = 7
logging.file = ${logs.dir}/${spring.application.name}.info.log
logging.level.root = INFO
logging.level.com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver = WARN
mybatis.delete.config.enable = true
mybatis.delete.ignore.table = t_ware_unite,t_ware_repulsion
management.endpoints.web.exposure.include = *
management.endpoint.health.show-details = always
spring.profiles.active = fat
spring.datasource.username = agent
spring.datasource.password = WrHNOhOGHR8yzMEgKvao
spring.datasource.druid.filters = stat
spring.datasource.druid.stat-view-servlet.enabled = true
spring.datasource.druid.stat-view-servlet.url-pattern = /druid/*
spring.datasource.druid.stat-view-servlet.reset-enable = false
spring.datasource.druid.stat-view-servlet.login-username = admin
spring.datasource.druid.stat-view-servlet.login-password = admin
spring.datasource.druid.stat-view-servlet.allow =
spring.datasource.driverClassName = com.mysql.jdbc.Driver
spring.datasource.type = com.alibaba.druid.pool.DruidDataSource
spring.datasource.druid.initial-size = 5
spring.datasource.druid.max-active = 30
spring.datasource.druid.min-idle = 5
spring.datasource.druid.max-wait = 60000
spring.datasource.druid.time-between-eviction-runs-millis = 60000
spring.datasource.druid.min-evictable-idle-time-millis = 300000
spring.datasource.druid.validation-query = SELECT 1 FROM DUAL
spring.datasource.druid.test-while-idle = true
spring.datasource.druid.max-pool-prepared-statement-per-connection-size = 50
spring.datasource.druid.connection-properties = druid.stat.mergeSql=true;druid.stat.slowSqlMillis=500
spring.datasource.druid.use-global-data-source-stat = true

# arthas??
#arthas.telnetPort = -1
#arthas.httpPort = -1
#arthas.ip = ${pod_ip}

# prometheus??
management.info.git.mode = full
management.endpoint.mappings.enabled = true
management.endpoint.httptrace.enabled = true
management.endpoint.shutdown.enabled = true
management.endpoint.shutdown.sensitive = false
management.endpoints.web.exposure.include[0] = *

management.health.mail.enabled = false

taobao.ma.url = http://gw.api.taobao.com/router/rest
taobao.ma.key = 32658202
taobao.ma.secret = 0ebde8fa8417f291b958d87265e996c6
spring.redis.cluster.hostandport = **********:7000;**********:7001;**********:7000;**********:7001;**********:7000;**********:7001
spring.redis.cluster.nodes = **********:7000,**********:7001,**********:7000,**********:7001,**********:7000,**********:7001
spring.redis.cluster.password = tThnBkJCgX
spring.redis.password = tThnBkJCgX
ribbon.UseIPAddrForServer = true
spring.cloud.nacos.discovery.server-addr = http://**********:8848;
spring.cloud.nacos.discovery.namespace = 63b6732e-80fc-49c2-ac83-4b09e119d48c
spring.cloud.nacos.discovery.ephemeral = true
spring.cloud.nacos.discovery.heart-beat-interval = 6000
spring.cloud.nacos.discovery.heart-beat-timeout = 12000
spring.cloud.nacos.discovery.ip-delete-timeout = 12000

#hystrix.threadpool.default.coreSize = 100
#hystrix.threadpool.default.maxQueueSize = 1500
#hystrix.threadpool.default.queueSizeRejectionThreshold = 1000
#hystrix.command.default.execution.timeout.enabled = false
spring.cloud.nacos.discovery.metadata.department = HC

#########################
#OSS??
ali.oss.endPoint = https://oss-cn-chengdu.aliyuncs.com
ali.oss.bucketName = sk-test-centermerchant
ali.oss.accessKeyId = LTAI5tQRo6iGTnyLZuYt3au7
ali.oss.accessKeySecret = ******************************
ali.oss.bucketUrl = https://sk-test-centermerchant.oss-cn-chengdu.aliyuncs.com


aliyun.oss.endpoint = oss-cn-chengdu.aliyuncs.com
aliyun.oss.bucket-name = sk-test-centermerchant
aliyun.oss.grant-expire-time = 1800
aliyun.oss.grant-max-content-length = 100
aliyun.oss.access-key-id = LTAI5tQRo6iGTnyLZuYt3au7
aliyun.oss.access-key-secret = ******************************
aliyun.oss.client.max-connections = 1024
aliyun.oss.client.socket-timeout = 50000
aliyun.oss.client.connection-timeout = 50000
aliyun.oss.client.connection-request-timeout = 3000
aliyun.oss.client.idle-connection-time = 600000
aliyun.oss.client.max-error-retry = 3
aliyun.oss.client.support-cname = true
aliyun.oss.client.sld-enabled = false
aliyun.oss.client.protocol = http
aliyun.oss.client.user-agent = aliyun-sdk-java

#############################
hydee.mq.server = 10.4.3.242:9876;10.4.3.243:9876

# ??????
hydee.stock.rturnstorage.back.nameServer = ${hydee.mq.server}
hydee.stock.rturnstorage.back.successTags = success
hydee.stock.rturnstorage.back.errorTags = error
hydee.stock.rturnstorage.back.cancelTags = cancel
hydee.stock.rturnstorage.back.topic = StockTopic
hydee.stock.rturnstorage.back.tags = rturnstorageEffectBack
hydee.stock.rturnstorage.back.groupname = rturnstorageEffectHandlerGroup

hydee.addwarepricegroupdetailforware.nameServer = ${hydee.mq.server}
hydee.addwarepricegroupdetailforware.successTags = success
hydee.addwarepricegroupdetailforware.errorTags = error
hydee.addwarepricegroupdetailforware.cancelTags = cancel
hydee.addwarepricegroupdetailforware.topic = PriceTopic
hydee.addwarepricegroupdetailforware.groupName = addWarePriceGroupDetailForWareGroup
hydee.addwarepricegroupdetailforware.tags = addWarePriceGroupDetailForWareTag

# ????
hydee.org-licence-sender.cancelTags = cancel
hydee.org-licence-sender.errorTags = error
hydee.org-licence-sender.groupname = addOrgLicence
hydee.org-licence-sender.nameServer = ${hydee.mq.server}
hydee.org-licence-sender.successTags = success
hydee.org-licence-sender.tags = addOrgLicence
hydee.org-licence-sender.topic = mq_org_licence_topic
# ????
hydee.org-sender.cancelTags = cancel
hydee.org-sender.errorTags = error
hydee.org-sender.groupname = modifyOrgBusinessScope
hydee.org-sender.nameServer = ${hydee.mq.server}
hydee.org-sender.successTags = success
hydee.org-sender.tags = modifyOrgBusinessScope
hydee.org-sender.topic = mq_org_topic
# ????
hydee.org-notification.cancelTags = cancel
hydee.org-notification.errorTags = error
hydee.org-notification.groupname = addOrgNotification
hydee.org-notification.nameServer = ${hydee.mq.server}
hydee.org-notification.successTags = success
hydee.org-notification.tags = addOrgNotification
hydee.org-notification.topic = mq_org_notification_topic
# ???????
hydee.org-class-sender.cancelTags = cancel
hydee.org-class-sender.errorTags = error
hydee.org-class-sender.groupname = createOrgToClass
hydee.org-class-sender.nameServer = ${hydee.mq.server}
hydee.org-class-sender.successTags = success
hydee.org-class-sender.tags = createOrgToClass
hydee.org-class-sender.topic = mq_org_class_topic

# ????
hydee.purchaseorder.nameServer = ${hydee.mq.server}
hydee.purchaseorder.successTags = success
hydee.purchaseorder.errorTags = error
hydee.purchaseorder.cancelTags = cancel
hydee.purchaseorder.topic = PurchaseOrderTopic
hydee.purchaseorder.tags = purchaseorder
hydee.purchaseorder.groupname = purchaseorder

# ????
hydee.addmantenance.nameServer = ${hydee.mq.server}
hydee.addmantenance.successTags = success
hydee.addmantenance.errorTags = error
hydee.addmantenance.cancelTags = cancel
hydee.addmantenance.topic = WareTopic
hydee.addmantenance.tags = modWareToMaintenanceTags
hydee.addmantenance.groupname = addToMaintenance

hydee.distribution.nameServer = ${hydee.mq.server}
hydee.distribution.successTags = success
hydee.distribution.errorTags = error
hydee.distribution.cancelTags = cancel
hydee.distribution.topic = WarehouseTopic
hydee.distribution.tags = distribution
hydee.distribution.groupname = distribution

hydee.initial.nameServer = ${hydee.mq.server}
hydee.initial.successTags = success
hydee.initial.errorTags = error
hydee.initial.cancelTags = cancel
hydee.initial.topic = WarehouseTopic
hydee.initial.tags = initial
hydee.initial.groupname = initial

hydee.adjust.nameServer = ${hydee.mq.server}
hydee.adjust.successTags = success
hydee.adjust.errorTags = error
hydee.adjust.cancelTags = cancel
hydee.adjust.topic = WarehouseTopic
hydee.adjust.tags = adjust
hydee.adjust.groupname = adjust

hydee.storereturn.nameServer = ${hydee.mq.server}
hydee.storereturn.successTags = success
hydee.storereturn.errorTags = error
hydee.storereturn.cancelTags = cancel
hydee.storereturn.topic = WarehouseTopic
hydee.storereturn.tags = storereturn
hydee.storereturn.groupname = storereturn

hydee.samplingcheck.nameServer = ${hydee.mq.server}
hydee.samplingcheck.successTags = success
hydee.samplingcheck.errorTags = error
hydee.samplingcheck.cancelTags = cancel
hydee.samplingcheck.topic = WarehouseTopic
hydee.samplingcheck.tags = samplingcheck
hydee.samplingcheck.groupname = samplingcheck

hydee.waretostall.nameServer = ${hydee.mq.server}
hydee.waretostall.successTags = success
hydee.waretostall.errorTags = error
hydee.waretostall.cancelTags = cancel
hydee.waretostall.topic = WareTopic
hydee.waretostall.tags = modWareToStallTags
hydee.waretostall.groupname = modWareToStallGroup

hydee.entrepotdeliverystock.nameServer = ${hydee.mq.server}
hydee.entrepotdeliverystock.successTags = success
hydee.entrepotdeliverystock.errorTags = error
hydee.entrepotdeliverystock.cancelTags = cancel
hydee.entrepotdeliverystock.topic = WarehouseTopic
hydee.entrepotdeliverystock.tags = entrepotDeliveryStock
hydee.entrepotdeliverystock.groupname = entrepotDeliveryGroup

hydee.entrepotstoragestock.nameServer = ${hydee.mq.server}
hydee.entrepotstoragestock.successTags = success
hydee.entrepotstoragestock.errorTags = error
hydee.entrepotstoragestock.cancelTags = cancel
hydee.entrepotstoragestock.topic = WarehouseTopic
hydee.entrepotstoragestock.tags = entrepotStorageSynWareAndSupplier
hydee.entrepotstoragestock.groupname = entrepotStorageGroup

# ????
hydee.storewarerecallhandler.nameServer = ${hydee.mq.server}
hydee.storewarerecallhandler.successTags = success
hydee.storewarerecallhandler.errorTags = error
hydee.storewarerecallhandler.cancelTags = cancel
hydee.storewarerecallhandler.topic = WarehouseTopic
hydee.storewarerecallhandler.tags = storewarerecallhandler
hydee.storewarerecallhandler.groupname = storewarerecallhandlerGroup

# ????????group
hydee.stockentrepotstoragestock.nameServer = ${hydee.mq.server}
hydee.stockentrepotstoragestock.successTags = success
hydee.stockentrepotstoragestock.errorTags = error
hydee.stockentrepotstoragestock.cancelTags = cancel
hydee.stockentrepotstoragestock.topic = WarehouseTopic
hydee.stockentrepotstoragestock.tags = entrepotStorageStock
hydee.stockentrepotstoragestock.groupname = stockentrepotStorageGroup

hydee.purchasereturns.nameServer = ${hydee.mq.server}
hydee.purchasereturns.successTags = success
hydee.purchasereturns.errorTags = error
hydee.purchasereturns.cancelTags = cancel
hydee.purchasereturns.topic = WarehouseTopic
hydee.purchasereturns.tags = purchasereturns
hydee.purchasereturns.groupname = purchasereturns

hydee.unqualifiedconfirm.nameServer = ${hydee.mq.server}
hydee.unqualifiedconfirm.successTags = success
hydee.unqualifiedconfirm.errorTags = error
hydee.unqualifiedconfirm.cancelTags = cancel
hydee.unqualifiedconfirm.topic = WarehouseTopic
hydee.unqualifiedconfirm.tags = unqualifiedConfirm
hydee.unqualifiedconfirm.groupname = unqualifiedConfirm

hydee.unqualifieddestruction.nameServer = ${hydee.mq.server}
hydee.unqualifieddestruction.successTags = success
hydee.unqualifieddestruction.errorTags = error
hydee.unqualifieddestruction.cancelTags = cancel
hydee.unqualifieddestruction.topic = WarehouseTopic
hydee.unqualifieddestruction.tags = unqualifiedDestruction
hydee.unqualifieddestruction.groupname = unqualifiedDestruction

hydee.storereceiptstock.nameServer = ${hydee.mq.server}
hydee.storereceiptstock.successTags = success
hydee.storereceiptstock.errorTags = error
hydee.storereceiptstock.cancelTags = cancel
hydee.storereceiptstock.topic = WarehouseTopic
hydee.storereceiptstock.tags = storeReceiptStock
hydee.storereceiptstock.groupname = storeReceiptGroup

hydee.storeacceptancestock.nameServer = ${hydee.mq.server}
hydee.storeacceptancestock.successTags = success
hydee.storeacceptancestock.errorTags = error
hydee.storeacceptancestock.cancelTags = cancel
hydee.storeacceptancestock.topic = WarehouseTopic
hydee.storeacceptancestock.tags = storeAcceptanceStock
hydee.storeacceptancestock.groupname = storeAcceptanceGroup

hydee.entrepotreturnstoragestock.nameServer = ${hydee.mq.server}
hydee.entrepotreturnstoragestock.successTags = success
hydee.entrepotreturnstoragestock.errorTags = error
hydee.entrepotreturnstoragestock.cancelTags = cancel
hydee.entrepotreturnstoragestock.topic = WarehouseTopic
hydee.entrepotreturnstoragestock.tags = entrepotReturnStorageStock
hydee.entrepotreturnstoragestock.groupname = entrepotReturnStorageGroup

hydee.profitlossstock.nameServer = ${hydee.mq.server}
hydee.profitlossstock.successTags = success
hydee.profitlossstock.errorTags = error
hydee.profitlossstock.cancelTags = cancel
hydee.profitlossstock.topic = WarehouseTopic
hydee.profitlossstock.tags = profitlossstock
hydee.profitlossstock.groupname = profitlossGroup

hydee.checkstock.nameServer = ${hydee.mq.server}
hydee.checkstock.successTags = success
hydee.checkstock.errorTags = error
hydee.checkstock.cancelTags = cancel
hydee.checkstock.topic = WarehouseTopic
hydee.checkstock.tags = checkStock
hydee.checkstock.groupname = checkStockGroup

hydee.warehousebatchadjust.nameServer = ${hydee.mq.server}
hydee.warehousebatchadjust.successTags = success
hydee.warehousebatchadjust.errorTags = error
hydee.warehousebatchadjust.cancelTags = cancel
hydee.warehousebatchadjust.topic = StockTopic
hydee.warehousebatchadjust.groupname = stockToWarehouse
hydee.warehousebatchadjust.tags = batchAdjust

hydee.stalladjust.nameServer = ${hydee.mq.server}
hydee.stalladjust.successTags = success
hydee.stalladjust.errorTags = error
hydee.stalladjust.cancelTags = cancel
hydee.stalladjust.topic = WarehouseTopic
hydee.stalladjust.tags = stallAdjust
hydee.stalladjust.groupname = stallAdjust

hydee.wareadjust.nameServer = ${hydee.mq.server}
hydee.wareadjust.successTags = success
hydee.wareadjust.errorTags = error
hydee.wareadjust.cancelTags = cancel
hydee.wareadjust.topic = WarehouseTopic
hydee.wareadjust.tags = wareAdjust
hydee.wareadjust.groupname = wareAdjust


#????
hydee.deliveryapply.nameServer = ${hydee.mq.server}
hydee.deliveryapply.successTags = success
hydee.deliveryapply.errorTags = error
hydee.deliveryapply.cancelTags = cancel
hydee.deliveryapply.topic = StoreTopic
hydee.deliveryapply.tags = deliveryApply
hydee.deliveryapply.groupname = deliveryApply

hydee.storetransfer.nameServer = ${hydee.mq.server}
hydee.storetransfer.successTags = success
hydee.storetransfer.errorTags = error
hydee.storetransfer.cancelTags = cancel
hydee.storetransfer.topic = StoreTopic
hydee.storetransfer.tags = storetransfer
hydee.storetransfer.groupname = storetransfer

# ????

#???---??
hydee.amqpcreateoutbound.nameServer = ${hydee.mq.server}
hydee.amqpcreateoutbound.successTags = success
hydee.amqpcreateoutbound.errorTags = error
hydee.amqpcreateoutbound.cancelTags = cancel
hydee.amqpcreateoutbound.topic = StockTopic
hydee.amqpcreateoutbound.tags = createOutbound
hydee.amqpcreateoutbound.groupname = syncCreateOutbound


#??---??,??
hydee.amqpcreatorder.nameServer = ${hydee.mq.server}
hydee.amqpcreatorder.successTags = success
hydee.amqpcreatorder.errorTags = error
hydee.amqpcreatorder.cancelTags = cancel
hydee.amqpcreatorder.topic = OrderTopic
hydee.amqpcreatorder.tags = createTags
hydee.amqpcreatorder.groupname = syncCashRecord



#????---??
hydee.amqprefundorder.nameServer = ${hydee.mq.server}
hydee.amqprefundorder.successTags = success
hydee.amqprefundorder.errorTags = error
hydee.amqprefundorder.cancelTags = cancel
hydee.amqprefundorder.topic = OrderTopic
hydee.amqprefundorder.tags = refundOrder
hydee.amqprefundorder.groupname = refundOrder
#????---??
hydee.cancelorder.nameServer = ${hydee.mq.server}
hydee.cancelorder.successTags = success
hydee.cancelorder.errorTags = error
hydee.cancelorder.cancelTags = cancel
hydee.cancelorder.topic = OrderTopic
hydee.cancelorder.tags = cancelorder
hydee.cancelorder.groupname = cancelorder

#????-???
hydee.trade.third-pay-sender.nameServer = ${hydee.mq.server}
hydee.trade.third-pay-sender.successTags = success
hydee.trade.third-pay-sender.errorTags = error
hydee.trade.third-pay-sender.cancelTags = cancel
hydee.trade.third-pay-sender.topic = TradeThirdPay
hydee.trade.third-pay-sender.groupname = TradeThirdPaySenderGroup
hydee.trade.third-pay-sender.tags = trade

hydee.trade.pay-result-listener.nameServer = ${hydee.mq.server}
hydee.trade.pay-result-listener.successTags = success
hydee.trade.pay-result-listener.errorTags = error
hydee.trade.pay-result-listener.cancelTags = cancel
hydee.trade.pay-result-listener.topic = PayResultTopic
hydee.trade.pay-result-listener.groupname = TradePayResultListenerGroup
hydee.trade.pay-result-listener.tags = trade

hydee.trade.order-settled-sender.nameServer = ${hydee.mq.server}
hydee.trade.order-settled-sender.successTags = success
hydee.trade.order-settled-sender.errorTags = error
hydee.trade.order-settled-sender.cancelTags = cancel
hydee.trade.order-settled-sender.topic = TradeOrderSettledTopic
hydee.trade.order-settled-sender.groupname = TradeOrderSettledSenderGroup
hydee.trade.order-settled-sender.tags = *
#20200810 ??????
hydee.trade.order-proof-listener.nameServer = ${hydee.mq.server}
hydee.trade.order-proof-listener.topic = StoreTopic
hydee.trade.order-proof-listener.groupname = TradeOrderProofListenerGroup
hydee.trade.order-proof-listener.tags = updateOrderState

# ????b2c?????
hydee.trade.oms-b2c-sender.nameServer = ${hydee.mq.server}
hydee.trade.oms-b2c-sender.topic = omsB2c
hydee.trade.oms-b2c-sender.groupname = TradeOmsB2cSenderGroup

# ????b2c?????
hydee.trade.oms-b2c-listener.nameServer = ${hydee.mq.server}
hydee.trade.oms-b2c-listener.topic = omsB2c
hydee.trade.oms-b2c-listener.tags = order2WarehouseBack||orderOutboundBack||refund2WarehouseBack||orderOutboundRefundBack
hydee.trade.oms-b2c-listener.groupname = TradeOmsB2cListenerGroup

# ????????????
hydee.trade.union-drug-outbound-listener.nameServer = ${hydee.mq.server}
hydee.trade.union-drug-outbound-listener.topic = StockTopic
hydee.trade.union-drug-outbound-listener.tags = yaoLianOutStock
hydee.trade.union-drug-outbound-listener.groupname = TradeUnionDrugOutboundListenerGroup

# ??????????
hydee.trade.union-drug-refund-sender.nameServer = ${hydee.mq.server}
hydee.trade.union-drug-refund-sender.topic = TradeOrderSettledTopic
hydee.trade.union-drug-refund-sender.groupname = TradeUnionDrugRefundSenderGroup
hydee.trade.union-drug-refund-sender.tags = *

hydee.promotion.order-settled-listener.nameServer = ${hydee.mq.server}
hydee.promotion.order-settled-listener.topic = TradeOrderSettledTopic
hydee.promotion.order-settled-listener.groupname = PromotionOrderSettledListenerGroup
hydee.promotion.order-settled-listener.tags = *

#?????? -- ZXJ -YW
hydee.createorderfail.nameServer = ${hydee.mq.server}
hydee.createorderfail.successTags = success
hydee.createorderfail.errorTags = error
hydee.createorderfail.cancelTags = cancel
hydee.createorderfail.topic = OrderTopic
hydee.createorderfail.tags = createOrderFail
hydee.createorderfail.groupname = createorderfail
#?? YW-ZXJ
hydee.savelocalorder.nameServer = ${hydee.mq.server}
hydee.savelocalorder.successTags = success
hydee.savelocalorder.errorTags = error
hydee.savelocalorder.cancelTags = cancel
hydee.savelocalorder.topic = OrderTopic
hydee.savelocalorder.tags = saveLocalOrder
hydee.savelocalorder.groupname = saveLocalOrder
#?????---???
hydee.orgstockul.nameServer = ${hydee.mq.server}
hydee.orgstockul.successTags = success
hydee.orgstockul.errorTags = error
hydee.orgstockul.cancelTags = cancel
hydee.orgstockul.topic = mq_org_topic
hydee.orgstockul.tags = modifyOrgBusinessScope
hydee.orgstockul.groupname = modifyOrgBusinessScope


#????? ---YW---WB
hydee.amqpmodifysampling.nameServer = ${hydee.mq.server}
hydee.amqpmodifysampling.successTags = success
hydee.amqpmodifysampling.errorTags = error
hydee.amqpmodifysampling.cancelTags = cancel
hydee.amqpmodifysampling.topic = WarehouseTopic
hydee.amqpmodifysampling.tags = samplingcheck
hydee.amqpmodifysampling.groupname = samplingcheck




#??????  CS-QT
hydee.unqualified.nameServer = ${hydee.mq.server}
hydee.unqualified.successTags = success
hydee.unqualified.errorTags = error
hydee.unqualified.cancelTags = cancel
hydee.unqualified.topic = WarehouseTopic
hydee.unqualified.tags = unqualifiedConfirm
hydee.unqualified.groupname = unqualifiedConfirm

#????????---???
hydee.defaultstockstall.nameServer = ${hydee.mq.server}
hydee.defaultstockstall.successTags = success
hydee.defaultstockstall.errorTags = error
hydee.defaultstockstall.cancelTags = cancel
hydee.defaultstockstall.topic = StockTopic
hydee.defaultstockstall.tags = defaultStockStall
hydee.defaultstockstall.groupname = defaultStockStall

#????

# ??
hydee.modifypricegroup.nameServer = ${hydee.mq.server}
hydee.modifypricegroup.successTags = success
hydee.modifypricegroup.errorTags = error
hydee.modifypricegroup.cancelTags = cancel
hydee.modifypricegroup.topic = WareTopic
hydee.modifypricegroup.groupname = modWareToPriceGroup
hydee.modifypricegroup.tags = modWareToPriceTags


# ???
hydee.amqpadjustprice.nameServer = ${hydee.mq.server}
hydee.amqpadjustprice.successTags = success
hydee.amqpadjustprice.errorTags = error
hydee.amqpadjustprice.cancelTags = cancel
hydee.amqpadjustprice.topic = PriceTopic
hydee.amqpadjustprice.groupname = adjustPrice
hydee.amqpadjustprice.tags = adjustPrice

hydee.amqpadjustpriceeffect.nameServer = ${hydee.mq.server}
hydee.amqpadjustpriceeffect.successTags = success
hydee.amqpadjustpriceeffect.errorTags = error
hydee.amqpadjustpriceeffect.cancelTags = cancel
hydee.amqpadjustpriceeffect.topic = PriceTopic
hydee.amqpadjustpriceeffect.groupname = adjustPrice
hydee.amqpadjustpriceeffect.tags = adjustPrice

# ?????
hydee.amqpcommonadjustprice.nameServer = ${hydee.mq.server}
hydee.amqpcommonadjustprice.successTags = success
hydee.amqpcommonadjustprice.errorTags = error
hydee.amqpcommonadjustprice.cancelTags = cancel
hydee.amqpcommonadjustprice.topic = PriceTopic
hydee.amqpcommonadjustprice.groupname = commonAdjustPrice
hydee.amqpcommonadjustprice.tags = commonAdjustPrice

# ???
hydee.amqppricegroupbusiness.nameServer = ${hydee.mq.server}
hydee.amqppricegroupbusiness.successTags = success
hydee.amqppricegroupbusiness.errorTags = error
hydee.amqppricegroupbusiness.cancelTags = cancel
hydee.amqppricegroupbusiness.topic = PriceTopic
hydee.amqppricegroupbusiness.groupname = changePriceGroupBusinessRela
hydee.amqppricegroupbusiness.tags = changePriceGroupBusinessRela

hydee.amqpupdateadjustprice.nameServer = ${hydee.mq.server}
hydee.amqpupdateadjustprice.successTags = success
hydee.amqpupdateadjustprice.errorTags = error
hydee.amqpupdateadjustprice.cancelTags = cancel
hydee.amqpupdateadjustprice.topic = PriceTopic
hydee.amqpupdateadjustprice.groupname = updateAdjustPrice
hydee.amqpupdateadjustprice.tags = updateAdjustPrice

hydee.amqpupdatepricegroupdetail.nameServer = ${hydee.mq.server}
hydee.amqpupdatepricegroupdetail.successTags = success
hydee.amqpupdatepricegroupdetail.errorTags = error
hydee.amqpupdatepricegroupdetail.cancelTags = cancel
hydee.amqpupdatepricegroupdetail.topic = PriceGroupUpdate
hydee.amqpupdatepricegroupdetail.groupname = priceGroupUpdateDetail
hydee.amqpupdatepricegroupdetail.tags = priceGroup


hydee.amqpchangepricegroupbusiness.nameServer = ${hydee.mq.server}
hydee.amqpchangepricegroupbusiness.successTags = success
hydee.amqpchangepricegroupbusiness.errorTags = error
hydee.amqpchangepricegroupbusiness.cancelTags = cancel
hydee.amqpchangepricegroupbusiness.topic = PriceTopic
hydee.amqpchangepricegroupbusiness.groupname = changePriceGroupBusinessRela
hydee.amqpchangepricegroupbusiness.tags = changePriceGroupBusinessRela

hydee.addpricegroup.nameServer = ${hydee.mq.server}
hydee.addpricegroup.successTags = success
hydee.addpricegroup.errorTags = error
hydee.addpricegroup.cancelTags = cancel
hydee.addpricegroup.topic = PriceTopic
hydee.addpricegroup.groupname = addPriceGroup
hydee.addpricegroup.tags = addPriceGroup

#??????
hydee.amqpadjustpricemsg.nameServer = ${hydee.mq.server}
hydee.amqpadjustpricemsg.successTags = success
hydee.amqpadjustpricemsg.errorTags = error
hydee.amqpadjustpricemsg.cancelTags = cancel
hydee.amqpadjustpricemsg.topic = PriceTopic
hydee.amqpadjustpricemsg.groupname = adjustPriceMsg
hydee.amqpadjustpricemsg.tags = commonAdjustPrice

hydee.amqpaddpricegroup.nameServer = ${hydee.mq.server}
hydee.amqpaddpricegroup.successTags = success
hydee.amqpaddpricegroup.errorTags = error
hydee.amqpaddpricegroup.cancelTags = cancel
hydee.amqpaddpricegroup.topic = PriceTopic
hydee.amqpaddpricegroup.groupname = addPriceGroup
hydee.amqpaddpricegroup.tags = addPriceGroup

#????
#--?????MQ
hydee.updateorderoutbound.nameServer = ${hydee.mq.server}
hydee.updateorderoutbound.successTags = success
hydee.updateorderoutbound.errorTags = error
hydee.updateorderoutbound.cancelTags = cancel
hydee.updateorderoutbound.topic = FinanceTopic
hydee.updateorderoutbound.groupname = updateOrderOutBound
hydee.updateorderoutbound.tags = updateOrderOutBound


#????===start
hydee.amqp.nameServer = ${hydee.mq.server}
hydee.amqp.successTags = success
hydee.amqp.errorTags = error
hydee.amqp.cancelTags = cancel
hydee.amqp.topic = mq_order_exchange
hydee.amqp.queue = mq_order_queue
hydee.amqp.groupname = my-group
hydee.amqp.tags = tags
hydee.createorder.nameServer = ${hydee.mq.server}
hydee.createorder.successTags = success
hydee.createorder.errorTags = error
hydee.createorder.cancelTags = cancel
hydee.createorder.topic = OrderTopic
hydee.createorder.groupname = saveLocalOrderCustomer
hydee.createorder.tags = saveLocalOrder
hydee.orgclass.cancelTags = cancel
hydee.orgclass.errorTags = error
hydee.orgclass.groupname = createOrgToClassCustomer
hydee.orgclass.nameServer = ${hydee.mq.server}
hydee.orgclass.successTags = success
hydee.orgclass.tags = createOrgToClass
hydee.orgclass.topic = mq_org_class_topic
#????===end

#????===start
hydee.amqpaddormodtostock.nameServer = ${hydee.mq.server}
hydee.amqpaddormodtostock.successTags = success
hydee.amqpaddormodtostock.errorTags = error
hydee.amqpaddormodtostock.cancelTags = cancel
hydee.amqpaddormodtostock.topic = WareTopic
hydee.amqpaddormodtostock.groupName = modWareToStockGroup
hydee.amqpaddormodtostock.tags = modWareToStockTags


#??-????
hydee.ware.addormod-to-warespecial.nameServer = ${hydee.mq.server}
hydee.ware.addormod-to-warespecial.successTags = success
hydee.ware.addormod-to-warespecial.errorTags = error
hydee.ware.addormod-to-warespecial.cancelTags = cancel
hydee.ware.addormod-to-warespecial.topic = WareTopic
hydee.ware.addormod-to-warespecial.groupName = modWareToWareSpecialGroup
hydee.ware.addormod-to-warespecial.tags = modWareToWareSpecialTags

#??-?????
hydee.ware.add-to-stockupperlower.nameServer = ${hydee.mq.server}
hydee.ware.add-to-stockupperlower.successTags = success
hydee.ware.add-to-stockupperlower.errorTags = error
hydee.ware.add-to-stockupperlower.cancelTags = cancel
hydee.ware.add-to-stockupperlower.topic = WareTopic
hydee.ware.add-to-stockupperlower.tags = modWareToStockUpperLowerTags
hydee.ware.add-to-stockupperlower.groupname = modWareToStockUpperLowerGroup
#????===end

#????
hydee.cancelordererr.nameServer = ${hydee.mq.server}
hydee.cancelordererr.successTags = succescs
hydee.cancelordererr.errorTags = error
hydee.cancelordererr.cancelTags = cancel
hydee.cancelordererr.topic = OrderTopic
hydee.cancelordererr.tags = updatePayRecode
hydee.cancelordererr.groupname = cancelorder

hydee.settlementorder.nameServer = ${hydee.mq.server}
hydee.settlementorder.successTags = success
hydee.settlementorder.errorTags = error
hydee.settlementorder.cancelTags = cancel
hydee.settlementorder.topic = OrderTopic
hydee.settlementorder.tags = settlementOrder
hydee.settlementorder.groupname = settlementOrder

hydee.refundsettlementorder.nameServer = ${hydee.mq.server}
hydee.refundsettlementorder.successTags = success




hydee.refundsettlementorder.errorTags = error
hydee.refundsettlementorder.cancelTags = cancel
hydee.refundsettlementorder.topic = OrderTopic
hydee.refundsettlementorder.tags = refundSettlementOrder
hydee.refundsettlementorder.groupname = refundSettlementOrder

# ????
hydee.pay.trade-third-pay-listener.nameServer = ${hydee.mq.server}


hydee.pay.trade-third-pay-listener.successTags = success
hydee.pay.trade-third-pay-listener.errorTags = error
hydee.pay.trade-third-pay-listener.cancelTags = cancel
hydee.pay.trade-third-pay-listener.topic = TradeThirdPay
hydee.pay.trade-third-pay-listener.groupname = tradeThirdPayGroup
hydee.pay.trade-third-pay-listener.tags = *


hydee.pay.trade-order-pay-listener.nameServer = ${hydee.mq.server}






hydee.pay.trade-order-pay-listener.topic = TradeOrderSettledTopic
hydee.pay.trade-order-pay-listener.groupname = PayOrderListenerGroup
hydee.pay.trade-order-pay-listener.tags = *



hydee.pay.notify-result-sender.nameServer = ${hydee.mq.server}
hydee.pay.notify-result-sender.successTags = success
hydee.pay.notify-result-sender.errorTags = error
hydee.pay.notify-result-sender.cancelTags = cancel
hydee.pay.notify-result-sender.topic = PayResultTopic
hydee.pay.notify-result-sender.groupname = notifyPayResultToBizSystem





hydee.pay.notify-result-sender.tags = notifyPayResultToBizSystem


#???????????????
hydee.notifyBalanceToTrade.nameServer = ${hydee.mq.server}
hydee.notifyBalanceToTrade.successTags = success
hydee.notifyBalanceToTrade.errorTags = error
hydee.notifyBalanceToTrade.cancelTags = cancel
hydee.notifyBalanceToTrade.topic = BalanceToTradeTopic


hydee.notifyBalanceToTrade.groupname = notifyBalanceToTrade
hydee.notifyBalanceToTrade.tags = notifyBalanceToTrade

#?????????????
hydee.notifyIntegralToTrade.nameServer = ${hydee.mq.server}
hydee.notifyIntegralToTrade.successTags = success
hydee.notifyIntegralToTrade.errorTags = error
hydee.notifyIntegralToTrade.cancelTags = cancel







hydee.notifyIntegralToTrade.topic = IntegeralToTradeTopic
hydee.notifyIntegralToTrade.groupname = notifyIntegeralToTrade
hydee.notifyIntegralToTrade.tags = notifyIntegeralToTrade


# ??
hydee.amqpaddormodtoprice.nameServer = ${hydee.mq.server}
hydee.amqpaddormodtoprice.successTags = success
hydee.amqpaddormodtoprice.errorTags = error
hydee.amqpaddormodtoprice.cancelTags = cancel
hydee.amqpaddormodtoprice.topic = WareTopic






hydee.amqpaddormodtoprice.groupName = modWareToPriceGroup
hydee.amqpaddormodtoprice.tags = modWareToPriceTags


# ???????????
hydee.orgpricegroup.cancelTags = cancel
hydee.orgpricegroup.errorTags = error
hydee.orgpricegroup.groupname = OrgPriceGroup
hydee.orgpricegroup.nameServer = ${hydee.mq.server}





hydee.orgpricegroup.successTags = success
hydee.orgpricegroup.tags = OrgPriceGroup
hydee.orgpricegroup.topic = mqOrgPriceGroup





hydee.priceforsupplierware.nameServer = ${hydee.mq.server}
hydee.priceforsupplierware.successTags = success
hydee.priceforsupplierware.errorTags = error
hydee.priceforsupplierware.cancelTags = cancel
hydee.priceforsupplierware.topic = PriceTopic
hydee.priceforsupplierware.groupname = adjustPriceForSupplierWare
hydee.priceforsupplierware.tags = commonAdjustPrice

hydee.customer.pay-result-listener.name-server = ${hydee.mq.server}
hydee.customer.pay-result-listener.topic = PayResultTopic
hydee.customer.pay-result-listener.group-name = payListenerGroup

hydee.customer.pay-result-listener.success-tags = success
hydee.customer.pay-result-listener.error-tags = error
hydee.customer.pay-result-listener.cancel-tags = cancel
hydee.customer.pay-result-listener.tags = cust-pay


hydee.customer.order-settled-listener.name-server = ${hydee.mq.server}
hydee.customer.order-settled-listener.topic = TradeOrderSettledTopic

hydee.customer.order-settled-listener.group-name = CustomerOrderSettledListenerGroup

hydee.customer.order-settled-listener.success-tags = success
hydee.customer.order-settled-listener.error-tags = error

hydee.customer.order-settled-listener.cancel-tags = cancel
hydee.customer.order-settled-listener.tags = *

# ?????????????????
hydee.customer.process-step-listener.nameServer = ${hydee.mq.server}
hydee.customer.process-step-listener.topic = processTopic
hydee.customer.process-step-listener.tags = lc_batch_made_card||lc_change_balance||lc_change_card||lc_change_integral||lc_change_level||lc_change_password||lc_change_status
hydee.customer.process-step-listener.groupname = CustProgressStepListenerGroup

hydee.customer.pay-sender.topic = TradeThirdPay

hydee.customer.pay-sender.name-server = ${hydee.mq.server}
hydee.customer.pay-sender.group-name = sendPayGroup
hydee.customer.pay-sender.success-tags = success
hydee.customer.pay-sender.error-tags = error
hydee.customer.pay-sender.cancel-tags = cancel
hydee.customer.pay-sender.tags = *
hydee.purchaseplan.nameServer = ${hydee.mq.server}
hydee.purchaseplan.successTags = success
hydee.purchaseplan.errorTags = error
hydee.purchaseplan.cancelTags = cancel
hydee.purchaseplan.topic = PurchasePlanTopic
hydee.purchaseplan.tags = purchasePlan
hydee.purchaseplan.groupname = purchasePlanGroup
hydee.updateBusinessOrDosage.nameServer = ${hydee.mq.server}
hydee.updateBusinessOrDosage.successTags = success
hydee.updateBusinessOrDosage.errorTags = error
hydee.updateBusinessOrDosage.cancelTags = cancel
hydee.updateBusinessOrDosage.topic = SupplierTopic
hydee.updateBusinessOrDosage.groupname = updateBusinessOrDosage
hydee.updateBusinessOrDosage.tags = updateBusinessOrDosage
hydee.accountstatement.nameServer = ${hydee.mq.server}
hydee.accountstatement.successTags = success
hydee.accountstatement.errorTags = error
hydee.accountstatement.cancelTags = cancel
hydee.accountstatement.topic = StoreTopic
hydee.accountstatement.groupname = updateOrderState
hydee.accountstatement.tags = updateOrderState

#????--laibing
hydee.purchaseadjustprice.nameServer = ${hydee.mq.server}
hydee.purchaseadjustprice.successTags = success
hydee.purchaseadjustprice.errorTags = error
hydee.purchaseadjustprice.cancelTags = cancel
hydee.purchaseadjustprice.topic = WarehouseTopic
hydee.purchaseadjustprice.tags = purchaseAdjustPriceTag
hydee.purchaseadjustprice.groupname = StockPurchaseAdjustPriceListenerGroup

#pos????--laibing
hydee.possaleorder.nameServer = ${hydee.mq.server}
hydee.possaleorder.successTags = success
hydee.possaleorder.errorTags = error
hydee.possaleorder.cancelTags = cancel
hydee.possaleorder.topic = TradeOrderSettledTopic
hydee.possaleorder.tags = *
hydee.possaleorder.groupname = StockPosOrderSettledListenerGroup

#????--????group
hydee.stockpurchaseadjustprice.nameServer = ${hydee.mq.server}
hydee.stockpurchaseadjustprice.successTags = success
hydee.stockpurchaseadjustprice.errorTags = error
hydee.stockpurchaseadjustprice.cancelTags = cancel
hydee.stockpurchaseadjustprice.topic = WarehouseTopic
hydee.stockpurchaseadjustprice.tags = purchaseAdjustPriceTag
hydee.stockpurchaseadjustprice.groupname = StockCenterPurchaseAdjustPriceListenerGroup

#?????---???
hydee.addstockupperlower.nameServer = ${hydee.mq.server}
hydee.addstockupperlower.successTags = success
hydee.addstockupperlower.errorTags = error
hydee.addstockupperlower.cancelTags = cancel
hydee.addstockupperlower.topic = WareTopic
hydee.addstockupperlower.tags = modWareToStockTags
hydee.addstockupperlower.groupname = modWareToStockGroup

#????????????
hydee.changequalifiedqty.nameServer = ${hydee.mq.server}
hydee.changequalifiedqty.successTags = success
hydee.changequalifiedqty.errorTags = error
hydee.changequalifiedqty.cancelTags = cancel
hydee.changequalifiedqty.topic = StockTopic
hydee.changequalifiedqty.tags = changeQualifiedQtyTag
hydee.changequalifiedqty.groupname = StockChangeQualifiedQtyListenerGroup

hydee.stockcenterpurchaseorder.nameServer = ${hydee.mq.server}
hydee.stockcenterpurchaseorder.successTags = success
hydee.stockcenterpurchaseorder.errorTags = error
hydee.stockcenterpurchaseorder.cancelTags = cancel
hydee.stockcenterpurchaseorder.topic = PurchaseOrderTopic
hydee.stockcenterpurchaseorder.tags = purchaseorder
hydee.stockcenterpurchaseorder.groupname = stockcenterpurchaseorder

hydee.initialsupplier.nameServer = ${hydee.mq.server}
hydee.initialsupplier.successTags = success
hydee.initialsupplier.errorTags = error
hydee.initialsupplier.cancelTags = cancel
hydee.initialsupplier.topic = WarehouseTopic
hydee.initialsupplier.tags = initial
hydee.initialsupplier.groupname = initialsupplier


# ?????mq??
hydee.trade.stock-out-bound-listener.nameServer = ${hydee.mq.server}
hydee.trade.stock-out-bound-listener.topic = StockNineOrderSettledTopic
hydee.trade.stock-out-bound-listener.groupname = TradeNineOutboundListenerGroup
hydee.trade.stock-out-bound-listener.tags = stockNine

# ??????MQ
hydee.trade.nine.order-settled-sender.nameServer = ${hydee.mq.server}
hydee.trade.nine.order-settled-sender.topic = TradeNineOrderSettledTopic
hydee.trade.nine.order-settled-sender.groupname = TradeNineOrderSettledSenderGroup
hydee.trade.nine.order-settled-sender.tags = nine

# ???????
hydee.stock.nine.order-settled-sender.nameServer = ${hydee.mq.server}
hydee.stock.nine.order-settled-sender.topic = StockNineOrderSettledTopic
hydee.stock.nine.order-settled-sender.groupname = StockNineOrderSettledSenderGroup
hydee.stock.nine.order-settled-sender.tags = stockNine

# ????????
hydee.stock.nine.order-settled-consumer.nameServer = ${hydee.mq.server}
hydee.stock.nine.order-settled-consumer.topic = TradeNineOrderSettledTopic
hydee.stock.nine.order-settled-consumer.groupname = TradeNineOrderSettledSenderGroup
hydee.stock.nine.order-settled-consumer.tags = nine


#?????
hydee.pingansaleorder.nameServer = ${hydee.mq.server}
hydee.pingansaleorder.successTags = success
hydee.pingansaleorder.errorTags = error
hydee.pingansaleorder.cancelTags = cancel
hydee.pingansaleorder.topic = StockTopic
hydee.pingansaleorder.tags = pingansaleorderTag
hydee.pingansaleorder.groupname = StockPINGANSaleOrderListenerGroup

hydee.pinganoutstock.nameServer = ${hydee.mq.server}
hydee.pinganoutstock.successTags = success
hydee.pinganoutstock.errorTags = error
hydee.pinganoutstock.cancelTags = cancel
hydee.pinganoutstock.topic = StockTopic
hydee.pinganoutstock.tags = pinganoutstockTag
hydee.pinganoutstock.groupname = StockPINGANOutStockListenerGroup

hydee.pinganrefundorder.nameServer = ${hydee.mq.server}
hydee.pinganrefundorder.successTags = success
hydee.pinganrefundorder.errorTags = error
hydee.pinganrefundorder.cancelTags = cancel
hydee.pinganrefundorder.topic = StockTopic
hydee.pinganrefundorder.tags = pinganrefundorderTag
hydee.pinganrefundorder.groupname = StockPINGANRefundOrderListenerGroup


# ???????????
hydee.trade.oms-order-settled-sender.nameServer = ${hydee.mq.server}
hydee.trade.oms-order-settled-sender.successTags = success
hydee.trade.oms-order-settled-sender.errorTags = error
hydee.trade.oms-order-settled-sender.cancelTags = cancel
hydee.trade.oms-order-settled-sender.topic = StockTopic
hydee.trade.oms-order-settled-sender.tags = pingansaleorderTag
hydee.trade.oms-order-settled-sender.groupname = TradePINGANSaleOrderSenderGroup

# ???????
hydee.trade.oms-stock-out-bound-listener.nameServer = ${hydee.mq.server}
hydee.trade.oms-stock-out-bound-listener.successTags = success
hydee.trade.oms-stock-out-bound-listener.errorTags = error
hydee.trade.oms-stock-out-bound-listener.cancelTags = cancel
hydee.trade.oms-stock-out-bound-listener.topic = StockTopic
hydee.trade.oms-stock-out-bound-listener.tags = pinganoutstockTag
hydee.trade.oms-stock-out-bound-listener.groupname = TradePINGANOutStockListenerGroup

# ?????????
hydee.trade.oms-refund-order-settled-sender.nameServer = ${hydee.mq.server}
hydee.trade.oms-refund-order-settled-sender.topic = StockTopic
hydee.trade.oms-refund-order-settled-sender.tags = pinganrefundorderTag
hydee.trade.oms-refund-order-settled-sender.groupname = TradePINGANRefundOrderSenderGroup
hydee.ware.first-platform-process.tags = first_platform_process
hydee.ware.first-platform-process.groupname = first_platform_process
hydee.ware.first-group-process.tags = first_group_process
hydee.ware.first-group-process.groupname = first_group_process
hydee.ware.first-company-process.tags = first_company_process
hydee.ware.first-company-process.groupname = first_company_process
hydee.ware.ware-platform-process.tags = ware_platform_process
hydee.ware.ware-platform-process.groupname = ware_platform_process
hydee.ware.ware-group-process.tags = ware_group_process
hydee.ware.ware-group-process.groupname = ware_group_process
hydee.ware.ware-company-process.tags = ware_company_process
hydee.ware.ware-company-process.groupname = ware_company_process
hydee.ware.first-platform-process.nameServer = ${hydee.mq.server}
hydee.ware.first-platform-process.successTags = success
hydee.ware.first-platform-process.errorTags = error
hydee.ware.first-platform-process.cancelTags = cancel
hydee.ware.first-platform-process.topic = processTopic
hydee.ware.first-group-process.nameServer = ${hydee.mq.server}
hydee.ware.first-group-process.successTags = success
hydee.ware.first-group-process.errorTags = error
hydee.ware.first-group-process.cancelTags = cancel
hydee.ware.first-group-process.topic = processTopic
hydee.ware.first-company-process.nameServer = ${hydee.mq.server}
hydee.ware.first-company-process.successTags = success
hydee.ware.first-company-process.errorTags = error
hydee.ware.first-company-process.cancelTags = cancel
hydee.ware.first-company-process.topic = processTopic
hydee.ware.ware-platform-process.nameServer = ${hydee.mq.server}
hydee.ware.ware-platform-process.successTags = success
hydee.ware.ware-platform-process.errorTags = error
hydee.ware.ware-platform-process.cancelTags = cancel
hydee.ware.ware-platform-process.topic = processTopic
hydee.ware.ware-group-process.nameServer = ${hydee.mq.server}
hydee.ware.ware-group-process.successTags = success
hydee.ware.ware-group-process.errorTags = error
hydee.ware.ware-group-process.cancelTags = cancel
hydee.ware.ware-group-process.topic = processTopic
hydee.ware.ware-company-process.nameServer = ${hydee.mq.server}
hydee.ware.ware-company-process.successTags = success
hydee.ware.ware-company-process.errorTags = error
hydee.ware.ware-company-process.cancelTags = cancel
hydee.ware.ware-company-process.topic = processTopic
hydee.processtask.nameServer = ${hydee.mq.server}
hydee.processtask.successTags = success
hydee.processtask.errorTags = error
hydee.processtask.cancelTags = cancel
hydee.processtask.topic = processTopic
hydee.processtask.tags = process
hydee.processtask.groupname = process

#??????????????mq
hydee.customer.change-card-sender.topic = ChangeCardTopic
hydee.customer.change-card-sender.name-server = ${hydee.mq.server}
hydee.customer.change-card-sender.group-name = changeCardGroup
hydee.customer.change-card-sender.success-tags = success
hydee.customer.change-card-sender.error-tags = error
hydee.customer.change-card-sender.cancel-tags = cancel
hydee.customer.change-card-sender.tags = *

hydee.customer.change-card-listener.topic = ChangeCardTopic
hydee.customer.change-card-listener.name-server = ${hydee.mq.server}
hydee.customer.change-card-listener.group-name = changeCardListenerGroup
hydee.customer.change-card-listener.success-tags = success
hydee.customer.change-card-listener.error-tags = error
hydee.customer.change-card-listener.cancel-tags = cancel
hydee.customer.change-card-listener.tags = *

#?????????????
hydee.storetransferautostart.nameServer = ${hydee.mq.server}
hydee.storetransferautostart.successTags = success
hydee.storetransferautostart.errorTags = error
hydee.storetransferautostart.cancelTags = cancel
hydee.storetransferautostart.topic = StoreTopic
hydee.storetransferautostart.tags = storetransfer
hydee.storetransferautostart.groupname = storetransferautostart
#?????????????
hydee.storetransferauto.nameServer = ${hydee.mq.server}
hydee.storetransferauto.successTags = success
hydee.storetransferauto.errorTags = error
hydee.storetransferauto.cancelTags = cancel
hydee.storetransferauto.topic = StockTopic
hydee.storetransferauto.tags = changeQualifiedQtyTag
hydee.storetransferauto.groupname = storetransferauto
#?????????????
hydee.initialstockauto.nameServer = ${hydee.mq.server}
hydee.initialstockauto.successTags = success
hydee.initialstockauto.errorTags = error
hydee.initialstockauto.cancelTags = cancel
hydee.initialstockauto.topic = StockTopic
hydee.initialstockauto.tags = changeQualifiedQtyTag
hydee.initialstockauto.groupname = initialstockauto
#  ??--?????
hydee.changepricetagqty.nameServer = ${hydee.mq.server}
hydee.changepricetagqty.successTags = success
hydee.changepricetagqty.errorTags = error
hydee.changepricetagqty.cancelTags = cancel
hydee.changepricetagqty.topic = StockTopic
hydee.changepricetagqty.tags = changeQualifiedQtyTag
hydee.changepricetagqty.groupname = StockChangePriceTagQtyListenerGroup
# ???????????
hydee.amqpadjustpriceforware.nameServer = ${hydee.mq.server}
hydee.amqpadjustpriceforware.successTags = success
hydee.amqpadjustpriceforware.errorTags = error
hydee.amqpadjustpriceforware.cancelTags = cancel
hydee.amqpadjustpriceforware.topic = PriceTopic
hydee.amqpadjustpriceforware.groupname = adjustPriceForWare
hydee.amqpadjustpriceforware.tags = commonAdjustPrice

#??????????
hydee.ware.batch.nameServer = ${hydee.mq.server}
hydee.ware.batch.successTags = success
hydee.ware.batch.errorTags = error
hydee.ware.batch.cancelTags = cancel
hydee.ware.batch.topic = WarehouseTopic
hydee.ware.batch.tags = initial
hydee.ware.batch.groupname = wareForBatch

#???????/??????
hydee.ware.entrepotstoragestock.nameServer = ${hydee.mq.server}
hydee.ware.entrepotstoragestock.successTags = success
hydee.ware.entrepotstoragestock.errorTags = error
hydee.ware.entrepotstoragestock.cancelTags = cancel
hydee.ware.entrepotstoragestock.topic = WarehouseTopic
hydee.ware.entrepotstoragestock.tags = entrepotStorageSynWareAndSupplier
hydee.ware.entrepotstoragestock.groupname = wareEntrepotStorageGroup

#?????????
hydee.instock.nameServer = ${hydee.mq.server}
hydee.instock.successTags = success
hydee.instock.errorTags = error
hydee.instock.cancelTags = cancel
hydee.instock.topic = StockTopic
hydee.instock.tags = instockTag
hydee.instock.groupname = StockInListenerGroup
hydee.offlinesalewareclass.cancelTags = cancel
hydee.offlinesalewareclass.errorTags = error
hydee.offlinesalewareclass.groupname = OrgOfflineSale
hydee.offlinesalewareclass.nameServer = ${hydee.mq.server}
hydee.offlinesalewareclass.successTags = success
hydee.offlinesalewareclass.tags = OrgOfflineSale
hydee.offlinesalewareclass.topic = mq_org_topic

#???????
hydee.omsfix.nameServer = ${hydee.mq.server}
hydee.omsfix.successTags = success
hydee.omsfix.errorTags = error
hydee.omsfix.cancelTags = cancel
hydee.omsfix.topic = StockTopic
hydee.omsfix.tags = omsfixTag
hydee.omsfix.groupname = OMSFixStockListenerGroup

#?????
hydee.mergetodelivery.nameServer = ${hydee.mq.server}
hydee.mergetodelivery.successTags = success
hydee.mergetodelivery.errorTags = error
hydee.mergetodelivery.cancelTags = cancel
hydee.mergetodelivery.topic = StoreTopic
hydee.mergetodelivery.tags = deliveryApply
hydee.mergetodelivery.groupname = StoreDeliveryApplyGroup

#??????
hydee.merge.nameServer = ${hydee.mq.server}
hydee.merge.successTags = success
hydee.merge.errorTags = error
hydee.merge.cancelTags = cancel
hydee.merge.topic = WarehouseTopic
hydee.merge.tags = merge
hydee.merge.groupname = EntrepotMergeToApply

#?????
hydee.add-to-price-delivery-group.nameServer = ${hydee.mq.server}
hydee.add-to-price-delivery-group.successTags = success
hydee.add-to-price-delivery-group.errorTags = error
hydee.add-to-price-delivery-group.cancelTags = cancel
hydee.add-to-price-delivery-group.topic = WareTopic
hydee.add-to-price-delivery-group.groupName = addWareToDeliveryGroup
hydee.add-to-price-delivery-group.tags = modWareToPriceTags

#????? sender
hydee.add-delivery-group-sender.nameServer = ${hydee.mq.server}
hydee.add-delivery-group-sender.successTags = success
hydee.add-delivery-group-sender.errorTags = error
hydee.add-delivery-group-sender.cancelTags = cancel
hydee.add-delivery-group-sender.topic = PriceTopic
hydee.add-delivery-group-sender.groupName = addDeliveryGroup
hydee.add-delivery-group-sender.tags = addDeliveryGroup

#????? listener
hydee.add-delivery-group-listener.nameServer = ${hydee.mq.server}
hydee.add-delivery-group-listener.successTags = success
hydee.add-delivery-group-listener.errorTags = error
hydee.add-delivery-group-listener.cancelTags = cancel
hydee.add-delivery-group-listener.topic = PriceTopic
hydee.add-delivery-group-listener.groupName = addDeliveryGroup
hydee.add-delivery-group-listener.tags = addDeliveryGroup

# ?????????????
hydee.orgdeliverypricegroup.nameServer = ${hydee.mq.server}
hydee.orgdeliverypricegroup.successTags = success
hydee.orgdeliverypricegroup.errorTags = error
hydee.orgdeliverypricegroup.cancelTags = cancel
hydee.orgdeliverypricegroup.groupName = OrgDeliveryPriceGroup
hydee.orgdeliverypricegroup.tags = OrgPriceGroup
hydee.orgdeliverypricegroup.topic = mqOrgPriceGroup

hydee.storetransfersourceconfig.cancelTags = cancel
hydee.storetransfersourceconfig.errorTags = error
hydee.storetransfersourceconfig.nameServer = ${hydee.mq.server}
hydee.storetransfersourceconfig.successTags = success
hydee.storetransfersourceconfig.groupname = storeTransferSourceConfig
hydee.storetransfersourceconfig.tags = OrgOfflineSale
hydee.storetransfersourceconfig.topic = mq_org_topic

# ???????????
hydee.addpricegroupforware.nameServer = ${hydee.mq.server}
hydee.addpricegroupforware.successTags = success
hydee.addpricegroupforware.errorTags = error
hydee.addpricegroupforware.cancelTags = cancel
hydee.addpricegroupforware.topic = PriceTopic
hydee.addpricegroupforware.groupName = addPriceGroupForWareGroup
hydee.addpricegroupforware.tags = addPriceGroupForWareTag

# ??
hydee.stocktakingtostock.nameServer = ${hydee.mq.server}
hydee.stocktakingtostock.successTags = success
hydee.stocktakingtostock.errorTags = error
hydee.stocktakingtostock.cancelTags = cancel
hydee.stocktakingtostock.topic = StockTakingTopic
hydee.stocktakingtostock.tags = revisedInventory
hydee.stocktakingtostock.groupname = StockTakingGroup

#??????mq???????
hydee.initialvoucherauto.nameServer = ${hydee.mq.server}
hydee.initialvoucherauto.successTags = success
hydee.initialvoucherauto.errorTags = error
hydee.initialvoucherauto.cancelTags = cancel
hydee.initialvoucherauto.topic = StockTopic
hydee.initialvoucherauto.tags = changeQualifiedQtyTag
hydee.initialvoucherauto.groupname = initialvoucherauto

#??????????????
hydee.initialaccountcurrentdetailauto.nameServer = ${hydee.mq.server}
hydee.initialaccountcurrentdetailauto.successTags = success
hydee.initialaccountcurrentdetailauto.errorTags = error
hydee.initialaccountcurrentdetailauto.cancelTags = cancel
hydee.initialaccountcurrentdetailauto.topic = StockTopic
hydee.initialaccountcurrentdetailauto.tags = changeQualifiedQtyTag
hydee.initialaccountcurrentdetailauto.groupname = initialaccountcurrentdetailauto
#??????POS?????Topic
hydee.alibabadrugtrace.nameServer = ${hydee.mq.server}
hydee.alibabadrugtrace.successTags = success
hydee.alibabadrugtrace.errorTags = error
hydee.alibabadrugtrace.cancelTags = cancel
hydee.alibabadrugtrace.topic = TradeOrderSettledTopic
hydee.alibabadrugtrace.tags = *
hydee.alibabadrugtrace.groupname = alibabadrugtrace

#????
hydee.entrepotremotereceiptstock.nameServer = ${hydee.mq.server}
hydee.entrepotremotereceiptstock.successTags = success
hydee.entrepotremotereceiptstock.errorTags = error
hydee.entrepotremotereceiptstock.cancelTags = cancel
hydee.entrepotremotereceiptstock.topic = WarehouseTopic
hydee.entrepotremotereceiptstock.tags = entrepotRemoteReceiptStock
hydee.entrepotremotereceiptstock.groupname = entrepotRemoteReceiptGroup

#???????????????
hydee.stock.automationstock.back.nameServer = ${hydee.mq.server}
hydee.stock.automationstock.back.successTags = success
hydee.stock.automationstock.back.errorTags = error
hydee.stock.automationstock.back.cancelTags = cancel
hydee.stock.automationstock.back.topic = StockTopic
hydee.stock.automationstock.back.groupname = autoMationStockBack
hydee.stock.automationstock.back.tags = autoMationStockBackGroup

#????????????????
hydee.warehouse.automationstock.nameServer = ${hydee.mq.server}
hydee.warehouse.automationstock.successTags = success
hydee.warehouse.automationstock.errorTags = error
hydee.warehouse.automationstock.cancelTags = cancel
hydee.warehouse.automationstock.topic = WarehouseTopic
hydee.warehouse.automationstock.tags = autoMationStock
hydee.warehouse.automationstock.groupname = autoMationStockGroup

#???? start
hydee.commission.document-process.nameServer = ${hydee.mq.server}
hydee.commission.document-process.successTags = success
hydee.commission.document-process.errorTags = error
hydee.commission.document-process.cancelTags = cancel
hydee.commission.document-process.tags = lc_commission||lc_adjust
hydee.commission.document-process.topic = processTopic
hydee.commission.document-process.groupname = commission_document_process

hydee.commission.order-settled-listener.nameServer = ${hydee.mq.server}
hydee.commission.order-settled-listener.topic = TradeOrderSettledTopic
hydee.commission.order-settled-listener.groupname = CommissionOrderSettledListenerGroup
hydee.commission.order-settled-listener.tags = *
#???? end

#??????? start
hydee.hdframe.msg-process.nameServer = ${hydee.mq.server}
hydee.hdframe.msg-process.groupname = hdframe_msg_process
hydee.hdframe.msg-process.topic = processTopic
hydee.hdframe.msg-process.tags = notificationMsg
hydee.hdframe.msg-process.successTags = success
hydee.hdframe.msg-process.errorTags = error
hydee.hdframe.msg-process.cancelTags = cancel
#??????? end

#????????? start
hydee.medinscloud.nameServer = ${hydee.mq.server}
hydee.medinscloud.successTags = success
hydee.medinscloud.errorTags = error
hydee.medinscloud.cancelTags = cancel
hydee.medinscloud.topic = StockTopic
hydee.medinscloud.tags = changeQualifiedQtyTag
hydee.medinscloud.groupname = medinscloudListenerGroup
#????????? end


#????? start
hydee.stockthirdstockout.nameServer = ${hydee.mq.server}
hydee.stockthirdstockout.groupname = StockThirdStockOutListenerGroup
hydee.stockthirdstockout.topic = omsB2c
hydee.stockthirdstockout.tags = saleOrderOutbound
hydee.stockthirdstockout.successTags = success
hydee.stockthirdstockout.errorTags = error
hydee.stockthirdstockout.cancelTags = cancel
#????? end

#????? start
hydee.stockthirdstockin.nameServer = ${hydee.mq.server}
hydee.stockthirdstockin.groupname = StockThirdStockInListenerGroup
hydee.stockthirdstockin.topic = omsB2c
hydee.stockthirdstockin.tags = refundOrderOutbound
hydee.stockthirdstockin.successTags = success
hydee.stockthirdstockin.errorTags = error
hydee.stockthirdstockin.cancelTags = cancel
#????? end

#??????? start
hydee.stocktotradethirdstockout.nameServer = ${hydee.mq.server}
hydee.stocktotradethirdstockout.groupname = StockThirdStockOutListenerGroup
hydee.stocktotradethirdstockout.topic = omsB2c
hydee.stocktotradethirdstockout.tags = orderOutboundBack
hydee.stocktotradethirdstockout.successTags = success
hydee.stocktotradethirdstockout.errorTags = error
hydee.stocktotradethirdstockout.cancelTags = cancel
#??????? end

#??????? start
hydee.stocktotradethirdstockin.nameServer = ${hydee.mq.server}
hydee.stocktotradethirdstockin.groupname = StockThirdStockInListenerGroup
hydee.stocktotradethirdstockin.topic = omsB2c
hydee.stocktotradethirdstockin.tags = orderOutboundRefundBack
hydee.stocktotradethirdstockin.successTags = success
hydee.stocktotradethirdstockin.errorTags = error
hydee.stocktotradethirdstockin.cancelTags = cancel
#??????? end



#????????? start
hydee.warehousefortradesendorder.nameServer = ${hydee.mq.server}
hydee.warehousefortradesendorder.groupname = WarehouseSendOrderListenerGroup
hydee.warehousefortradesendorder.topic = omsB2c
hydee.warehousefortradesendorder.tags = order2Warehouse
hydee.warehousefortradesendorder.successTags = success
hydee.warehousefortradesendorder.errorTags = error
hydee.warehousefortradesendorder.cancelTags = cancel
#????????? end

#????????? start
hydee.warehousefortradecancelorder.nameServer = ${hydee.mq.server}
hydee.warehousefortradecancelorder.groupname = WarehouseCancelOrderListenerGroup
hydee.warehousefortradecancelorder.topic = omsB2c
hydee.warehousefortradecancelorder.tags = cancel2Warehouse
hydee.warehousefortradecancelorder.successTags = success
hydee.warehousefortradecancelorder.errorTags = error
hydee.warehousefortradecancelorder.cancelTags = cancel
#????????? end


#????????? start
hydee.warehousefortradereturnorder.nameServer = ${hydee.mq.server}
hydee.warehousefortradereturnorder.groupname = WarehouseReturnOrderListenerGroup
hydee.warehousefortradereturnorder.topic = omsB2c
hydee.warehousefortradereturnorder.tags = refund2Warehouse
hydee.warehousefortradereturnorder.successTags = success
hydee.warehousefortradereturnorder.errorTags = error
hydee.warehousefortradereturnorder.cancelTags = cancel
#????????? end

#??????????? start
hydee.warehousefortradebacksendorder.nameServer = ${hydee.mq.server}
hydee.warehousefortradebacksendorder.groupname = WarehouseBackSendOrderListenerGroup
hydee.warehousefortradebacksendorder.topic = omsB2c
hydee.warehousefortradebacksendorder.tags = order2WarehouseBack
hydee.warehousefortradebacksendorder.successTags = success
hydee.warehousefortradebacksendorder.errorTags = error
hydee.warehousefortradebacksendorder.cancelTags = cancel
#??????????? end

#??????????? start
hydee.warehousefortradebackreturnorder.nameServer = ${hydee.mq.server}
hydee.warehousefortradebackreturnorder.groupname = WarehouseBackReturnOrderListenerGroup
hydee.warehousefortradebackreturnorder.topic = omsB2c
hydee.warehousefortradebackreturnorder.tags = refund2WarehouseBack
hydee.warehousefortradebackreturnorder.successTags = success
hydee.warehousefortradebackreturnorder.errorTags = error
hydee.warehousefortradebackreturnorder.cancelTags = cancel
#??????????? end

#?????????????????????? start
hydee.entrepotstoragesynwareandsupplier.nameServer = ${hydee.mq.server}
hydee.entrepotstoragesynwareandsupplier.successTags = success
hydee.entrepotstoragesynwareandsupplier.errorTags = error
hydee.entrepotstoragesynwareandsupplier.cancelTags = cancel
hydee.entrepotstoragesynwareandsupplier.topic = WarehouseTopic
hydee.entrepotstoragesynwareandsupplier.tags = entrepotStorageSynWareAndSupplier
hydee.entrepotstoragesynwareandsupplier.groupname = entrepotStorageGroup
#?????????????????????? end

#???????????? start
hydee.warehouse.stockchangelog.nameServer = ${hydee.mq.server}
hydee.warehouse.stockchangelog.successTags = success
hydee.warehouse.stockchangelog.errorTags = error
hydee.warehouse.stockchangelog.cancelTags = cancel
hydee.warehouse.stockchangelog.topic = WarehouseTopic
hydee.warehouse.stockchangelog.tags = synChangeLogWithStockAndStore
hydee.warehouse.stockchangelog.groupname = stockChangeLogGroup
#???????????? end

#????????????????? start
hydee.store.stockchangelog.nameServer = ${hydee.mq.server}
hydee.store.stockchangelog.successTags = success
hydee.store.stockchangelog.errorTags = error
hydee.store.stockchangelog.cancelTags = cancel
hydee.store.stockchangelog.topic = WarehouseTopic
hydee.store.stockchangelog.tags = synChangeLogWithStockAndStore
hydee.store.stockchangelog.groupname = storeChangeLogGroup
#???????????? end

#???????????????? start
hydee.stock.stockchangelog.nameServer = ${hydee.mq.server}
hydee.stock.stockchangelog.successTags = success
hydee.stock.stockchangelog.errorTags = error
hydee.stock.stockchangelog.cancelTags = cancel
hydee.stock.stockchangelog.topic = WarehouseTopic
hydee.stock.stockchangelog.tags = synChangeLogWithStockAndStore
hydee.stock.stockchangelog.groupname = stockChangeLogGroup
#???????????????? end

#??????????? start
hydee.yaolianoutstock.nameServer = ${hydee.mq.server}
hydee.yaolianoutstock.successTags = success
hydee.yaolianoutstock.errorTags = error
hydee.yaolianoutstock.cancelTags = cancel
hydee.yaolianoutstock.topic = StockTopic
hydee.yaolianoutstock.tags = yaoLianOutStock
hydee.yaolianoutstock.groupname = yaoLianOutStock
#??????????? end
hydee.ware-delete-to-price.successTags = success
hydee.ware-delete-to-price.errorTags = error
hydee.ware-delete-to-price.cancelTags = cancel
hydee.ware-delete-to-price.topic = WareTopic
hydee.ware-delete-to-price.groupName = deleteWareToPrice
hydee.ware-delete-to-price.tags = deleteWareToPriceTags
hydee.ware-delete-to-price.nameServer = ${hydee.mq.server}

#?????????????????
hydee.remotereceiptpurchase.nameServer = ${hydee.mq.server}
hydee.remotereceiptpurchase.successTags = success
hydee.remotereceiptpurchase.errorTags = error
hydee.remotereceiptpurchase.cancelTags = cancel
hydee.remotereceiptpurchase.topic = WarehouseTopic
hydee.remotereceiptpurchase.tags = remoteReceiptPurchase
hydee.remotereceiptpurchase.groupname = remoteReceiptPurchaseGroup

#????
hydee.data-sync.successTags = success
hydee.data-sync.errorTags = error
hydee.data-sync.cancelTags = cancel
hydee.data-sync.nameServer = ${hydee.mq.server}
hydee.data-sync.topic = DataSyncTopic
hydee.data-sync.gsp.supplier-info.groupName = dataSyncSupplier
hydee.data-sync.gsp.supplier-info.tags = supplierInfoTags
hydee.data-sync.gsp.organization-info.groupName = dataSyncOrganization
hydee.data-sync.gsp.organization-info.tags = organizationInfoTags
hydee.data-sync.gsp.licence-info.groupName = dataSyncLicence
hydee.data-sync.gsp.licence-info.tags = licenceInfoTags
hydee.data-sync.gsp.company-ware-info.groupName = dataSyncCompanyWare
hydee.data-sync.gsp.company-ware-info.tags = companyWareInfoTags
hydee.data-sync.gsp.platform-ware-info.groupName = dataSyncPlatformWare
hydee.data-sync.gsp.platform-ware-info.tags = platformWareInfoTags
hydee.data-sync.gsp.quality-ware-info.groupName = dataSyncQualityWare
hydee.data-sync.gsp.quality-ware-info.tags = qualityWareInfoTags
hydee.data-sync.gsp.supplier-mandator-info.groupName = dataSyncSupplierMandator
hydee.data-sync.gsp.supplier-mandator-info.tags = SupplierMandatorInfoTags
hydee.data-sync.gsp.parameter-info.groupName = dataSyncParameterInfo
hydee.data-sync.gsp.parameter-info.tags = ParameterInfoTags

hydee.ware.purchaseadjustprice.nameServer = ${hydee.mq.server}
hydee.ware.purchaseadjustprice.successTags = success
hydee.ware.purchaseadjustprice.errorTags = error
hydee.ware.purchaseadjustprice.cancelTags = cancel
hydee.ware.purchaseadjustprice.topic = WarehouseTopic
hydee.ware.purchaseadjustprice.tags = purchaseAdjustPriceTag
hydee.ware.purchaseadjustprice.groupname = WarePriceAdjustmentListenerGroup

# ????????????????
hydee.purchase.store-to-supplier-purchase.sender.nameServer = ${hydee.mq.server}
hydee.purchase.store-to-supplier-purchase.sender.topic = PurchaseOrderTopic
hydee.purchase.store-to-supplier-purchase.sender.tags = storeToSupplierPurchaseTag
hydee.purchase.store-to-supplier-purchase.sender.groupname = storeToSupplierPurchaseSenderGroup

# ????????????????
hydee.warehouse.store-to-supplier-purchase.listener.nameServer = ${hydee.mq.server}
hydee.warehouse.store-to-supplier-purchase.listener.groupname = storeToSupplierPurchaseListenerGroup
hydee.warehouse.store-to-supplier-purchase.listener.topic = PurchaseOrderTopic
hydee.warehouse.store-to-supplier-purchase.listener.tags = storeToSupplierPurchaseTag

hydee.ware-save-quasi-by-order.nameServer = ${hydee.mq.server}
hydee.ware-save-quasi-by-order.successTags = success
hydee.ware-save-quasi-by-order.errorTags = error
hydee.ware-save-quasi-by-order.cancelTags = cancel
hydee.ware-save-quasi-by-order.topic = QuasiWareInfo
hydee.ware-save-quasi-by-order.tags = syncQuasiWareInfo
hydee.ware-save-quasi-by-order.groupname = quasiWareInfoGroup

hydee.ware-save-quasi-consumer.nameServer = ${hydee.mq.server}
hydee.ware-save-quasi-consumer.successTags = success
hydee.ware-save-quasi-consumer.errorTags = error
hydee.ware-save-quasi-consumer.cancelTags = cancel
hydee.ware-save-quasi-consumer.topic = QuasiWareInfo
hydee.ware-save-quasi-consumer.tags = syncQuasiWareInfo
hydee.ware-save-quasi-consumer.groupname = quasiWareInfoGroup
hydee.ware-save-quasi-consumer.consumeMessageOrderly = true
