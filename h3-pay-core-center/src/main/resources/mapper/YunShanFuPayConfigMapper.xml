<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hydee.h3.pay.dao.PayWayDao">
    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , pay_way_code, pay_way_name, logo, remark, web_priority, pay_priority, refund_priority, is_delete, create_time,
    create_user, modify_time, modify_user,channel_source
    </sql>


    <select id="findPayWayByChannel" resultType="com.hydee.h3.internal.api.paycore.bean.response.wx.PayWayResponseVO">
        SELECT DISTINCT
        a.pay_way_code,
        a.pay_way_name,
        a.logo AS icon,
        a.remark,
        a.web_priority as priority,
        ( CASE WHEN a.pay_priority > #{payWayRequestVO.payPriority} THEN 1 ELSE 2 END) AS `enable`
        FROM
        t_pay_way a
        LEFT JOIN t_pay_way_detail c ON a.id = c.pay_way_id
        LEFT JOIN t_pay_channel b ON c.pay_channel_code = b.pay_channel_code
        WHERE
        c.pay_channel_code in
        <foreach collection="payChannelCodeList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        AND
        <foreach collection="payWayRequestVO.sourceList" item="item" open="(" close=")" separator="or">
            b.source LIKE CONCAT('%"',#{item},'"%')
        </foreach>
        AND
        <foreach collection="payWayRequestVO.sourceList" item="item" open="(" close=")" separator="or">
            a.channel_source LIKE CONCAT('%"',#{item},'"%')
        </foreach>
        AND a.is_delete = ${@com.hydee.h3.basedata.enums.BooleanEnum@BOOLEAN_FALSE.getValue()}
        AND b.is_delete = ${@com.hydee.h3.basedata.enums.BooleanEnum@BOOLEAN_FALSE.getValue()}
        AND b.can_third_status = #{payWayRequestVO.canThirdStatus}
        order by priority
    </select>

</mapper>
