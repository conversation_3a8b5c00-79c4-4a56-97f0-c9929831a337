<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.hydee.h3</groupId>
        <artifactId>h3-pay-core</artifactId>
        <version>1.0.1-SNAPSHOT</version>
    </parent>

    <groupId>com.hydee.h3</groupId>
    <artifactId>h3-pay-core-center</artifactId>
    <version>1.0.1-SNAPSHOT</version>
    <name>h3-pay-core-center</name>
    <modelVersion>4.0.0</modelVersion>
    <description>支付中心</description>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <skipTests>true</skipTests>
        <api.env>dev</api.env>
        <hydee.hdframe.version>1.5.7-SNAPSHOT</hydee.hdframe.version>
        <hydee.api.basic.version>2.2.6-${api.env}-SNAPSHOT</hydee.api.basic.version>
        <hydee.api.paycore.version>1.0.3-${api.env}-SNAPSHOT</hydee.api.paycore.version>
        <hydee.api.trade.version>2.1.6-${api.env}-SNAPSHOT</hydee.api.trade.version>
        <hydee.api.customer.version>2.0.2-${api.env}-SNAPSHOT</hydee.api.customer.version>
        <redission.version>1.0.0-RELEASE</redission.version>
        <yxt-wechatrobot.version>1.0-SNAPSHOT</yxt-wechatrobot.version>
        <yxt-xxl-job-starter.version>4.2.0</yxt-xxl-job-starter.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>net.sf.json-lib</groupId>
            <artifactId>json-lib</artifactId>
            <version>2.4</version>
            <classifier>jdk15</classifier>
        </dependency>

        <dependency>
            <groupId>com.hydee.h3</groupId>
            <artifactId>h3-hdframe-base</artifactId>
            <version>${hydee.hdframe.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.xuxueli</groupId>
                    <artifactId>xxl-job-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.hydee.common</groupId>
                    <artifactId>common-job</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.taobao.arthas</groupId>
                    <artifactId>arthas-spring-boot-starter</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--<dependency>
            <groupId>com.hydee</groupId>
            <artifactId>hydee-redis-client-common</artifactId>
            <version>1.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>redis.clients</groupId>
                    <artifactId>jedis</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.lettuce</groupId>
                    <artifactId>lettuce-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>-->

        <dependency>
            <groupId>com.hydee.h3</groupId>
            <artifactId>h3-internal-api-basic-setting</artifactId>
            <version>${hydee.api.basic.version}</version>
        </dependency>
        <dependency>
            <groupId>com.hydee.h3</groupId>
            <artifactId>h3-internal-api-paycore</artifactId>
            <version>${hydee.api.paycore.version}</version>
        </dependency>
        <dependency>
            <groupId>com.alipay.sdk</groupId>
            <artifactId>alipay-sdk-java</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.zxing</groupId>
            <artifactId>core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.zxing</groupId>
            <artifactId>javase</artifactId>
        </dependency>
        <dependency>
            <groupId>com.squareup.okhttp</groupId>
            <artifactId>okhttp</artifactId>
        </dependency>
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
        </dependency>
        <dependency>
            <groupId>com.squareup.okio</groupId>
            <artifactId>okio</artifactId>
        </dependency>
        <dependency>
            <groupId>commons-beanutils</groupId>
            <artifactId>commons-beanutils</artifactId>
        </dependency>
        <dependency>
            <groupId>com.hydee.common</groupId>
            <artifactId>common-file-load</artifactId>
        </dependency>
        <dependency>
            <groupId>com.hydee.common</groupId>
            <artifactId>common-redission</artifactId>
            <version>${redission.version}</version>
        </dependency>
        <dependency>
            <groupId>com.github.wechatpay-apiv3</groupId>
            <artifactId>wechatpay-apache-httpclient</artifactId>
            <version>0.4.2</version>
        </dependency>
        <!--        组织机构引入-->
        <dependency>
            <groupId>com.hydee.h3</groupId>
            <artifactId>h3-internal-api-organization</artifactId>
            <version>2.3.0-${api.env}-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.huifu.bspay.sdk</groupId>
            <artifactId>dg-java-sdk</artifactId>
            <version>3.0.9</version>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>com.huifu.bspay.sdk</groupId>-->
<!--            <artifactId>opps-bspay-java-sdk</artifactId>-->
<!--            <version>2.1.6-SNAPSHOT</version>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>com.huifu.bspay.sdk</groupId>-->
<!--            <artifactId>bspay-java-sdk-sample</artifactId>-->
<!--            <version>2.1.6-SNAPSHOT</version>-->
<!--        </dependency>-->
        <dependency>
            <groupId>com.yxt</groupId>
            <artifactId>yxt-common-alarm</artifactId>
            <version>4.1.0</version>
        </dependency>
        <dependency>
            <groupId>org.apache.rocketmq</groupId>
            <artifactId>rocketmq-spring-boot-starter</artifactId>
            <version>2.0.3</version>
        </dependency>
        <dependency>
            <groupId>org.apache.rocketmq</groupId>
            <artifactId>rocketmq-client</artifactId>
            <version>4.5.1</version>
        </dependency>
        <dependency>
            <groupId>commons-collections</groupId>
            <artifactId>commons-collections</artifactId>
            <version>3.2.2</version>
        </dependency>
        <dependency>
            <groupId>org.json</groupId>
            <artifactId>json</artifactId>
            <version>20210307</version>
        </dependency>
        <dependency>
            <groupId>com.tencent.mip</groupId>
            <artifactId>med-request-data-sdk</artifactId>
            <version>2.0.8</version>
        </dependency>

        <dependency>
            <groupId>com.yxt</groupId>
            <artifactId>yxt-wechatrobot</artifactId>
            <version>${yxt-wechatrobot.version}</version>
        </dependency>

        <!-- 解决跨JVM的ID生成 -->
        <dependency>
            <groupId>com.hydee.common</groupId>
            <artifactId>common-distributed-id</artifactId>
            <version>1.5.8-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>log4j</groupId>
                    <artifactId>log4j</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-log4j12</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- hippo4j -->
        <dependency>
            <groupId>cn.hydee</groupId>
            <artifactId>hydee-hippo4j-spring-boot-starter</artifactId>
            <version>3.1.0-SNAPSHOT</version>
        </dependency>
        <!-- xxljob -->
        <dependency>
            <groupId>com.yxt</groupId>
            <artifactId>yxt-xxljob-spring-boot-starter</artifactId>
            <version>${yxt-xxl-job-starter.version}</version>
        </dependency>
    </dependencies>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.xuxueli</groupId>
                <artifactId>xxl-job-core</artifactId>
                <version>2.4.1</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <target>${java.version}</target>
                    <source>${java.version}</source>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <finalName>${project.name}</finalName>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
